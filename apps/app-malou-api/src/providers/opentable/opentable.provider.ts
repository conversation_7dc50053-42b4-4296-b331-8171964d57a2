import axios, { AxiosInstance } from 'axios';
import { DateTime } from 'luxon';
import { inject, singleton } from 'tsyringe';
import { DeepPartial } from 'utility-types';

import { MalouErrorCode, openTableCookieKey, PlatformKey, platformsKeys, TimeInSeconds } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { InjectionToken } from ':helpers/injection';
import { logger } from ':helpers/logger';
import PuppeteerService, { SandboxResponse } from ':microservices/puppeteer-service';
import { reviewsFetchCounter } from ':modules/reviews/reviews.metrics';
import { Cache } from ':plugins/cache';
import { OpenTablePort } from ':providers/opentable/interfaces/opentable.port.interface';
import {
    OpenTableLightRestaurant,
    OpenTableLightRestaurantValidator,
    OpenTableRestaurant,
    OpentableSpecialHours,
} from ':providers/opentable/interfaces/opentable.restaurant.interface';
import {
    OpenTableReview,
    OpenTableReviewResponse,
    OpenTableReviewResponseValidator,
    OpenTableTodayRatingResponseValidator,
} from ':providers/opentable/interfaces/opentable.reviews.interface';
import { OpenTableNewSchedule, OpenTableScheduleGetResult } from ':providers/opentable/interfaces/opentable.schedules.interface';
import { OpenTableShift } from ':providers/opentable/interfaces/opentable.shifts.interface';
import { OPENTABLE_PUPPETEER_COOKIE_SCRAPPING_CODE } from ':providers/opentable/opentable.puppeteer';

@singleton()
export class OpenTableProvider implements OpenTablePort {
    private readonly _axiosInstance: AxiosInstance;

    constructor(
        private readonly _puppeteerService: PuppeteerService,
        @inject(InjectionToken.Cache) private readonly _cache: Cache
    ) {
        const axiosInstance = axios.create({
            baseURL: platformsKeys.OPENTABLE.apiUrl,
            headers: {
                // those headers are possibly not needed but I added them to be as close as possible to the original request
                accept: '*/*',
                'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8,fr;q=0.7',
                'sec-ch-ua': '"Not A(Brand";v="99", "Google Chrome";v="121", "Chromium";v="121"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"macOS"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'Referrer-Policy': 'strict-origin-when-cross-origin',
                authority: 'guestcenter.opentable.com',
            },
        });
        this._axiosInstance = axiosInstance;
    }

    async getLightRestaurant({ socialId }: { socialId: string }): Promise<OpenTableLightRestaurant> {
        const cookieFromCache = await this._getCookieFromCache();

        const response = await this._axiosInstance.get(`/gcrestaurant/api/restaurants/${socialId}`, {
            headers: {
                Cookie: cookieFromCache,
            },
        });

        return OpenTableLightRestaurantValidator.parse(response.data);
    }

    async getRestaurant({ socialId }: { socialId: string }): Promise<OpenTableRestaurant> {
        const cookieFromCache = await this._getCookieFromCache();

        const response = await this._axiosInstance.get(`/restaurantAdminService/restaurant-admin/v3/restaurants/${socialId}`, {
            headers: {
                Cookie: cookieFromCache,
            },
        });

        return response.data;
    }

    async getAllReviews({ socialId }: { socialId: string }): Promise<OpenTableReview[]> {
        const cookieFromCache = await this._getCookieFromCache();

        let page = 0;
        // next number is a max iteration to prevent being stuck in an infinite loop
        const maxIterations = 500;
        const reviews: OpenTableReview[] = [];
        let result: OpenTableReviewResponse | null = null;
        try {
            do {
                result = await this._fetchReviewsPage({ socialId, page, cookie: cookieFromCache });
                reviews.push(...result.data);
                page += 1;
            } while (result.meta.limit * page < result.meta.totalCount && page < maxIterations);
        } catch (error) {
            reviewsFetchCounter.add(1, {
                source: PlatformKey.OPENTABLE,
                status: 'failure',
            });
            throw error;
        }
        reviewsFetchCounter.add(1, {
            source: PlatformKey.OPENTABLE,
            status: 'success',
        });
        return reviews;
    }

    async getTodayRating({ socialId }: { socialId: string }): Promise<number> {
        const cookieFromCache = await this._getCookieFromCache();

        const response = await this._axiosInstance.get(`/aggregator/reviewsummary/restaurant/${socialId}?includeRatings=false`, {
            headers: {
                Cookie: cookieFromCache,
            },
        });

        const data = OpenTableTodayRatingResponseValidator.parse(response.data);

        return data.summary.recommendRating;
    }

    async patchRestaurant({
        socialId,
        openTableRestaurant,
    }: {
        socialId: string;
        openTableRestaurant: DeepPartial<OpenTableRestaurant>;
    }): Promise<OpenTableRestaurant> {
        const cookieFromCache = await this._getCookieFromCache();

        const response = await this._axiosInstance.patch<OpenTableRestaurant>(
            `gca-profile/restaurantAdminService/restaurant-admin/v3/restaurants/${socialId}`,
            openTableRestaurant,
            {
                headers: {
                    Cookie: cookieFromCache,
                },
            }
        );

        return response.data;
    }

    async closeSpecialHours({
        socialId,
        openTableSpecialHours,
    }: {
        socialId: string;
        openTableSpecialHours: OpentableSpecialHours;
    }): Promise<void> {
        const cookieFromCache = await this._getCookieFromCache();

        const suffix = 'https://guestcenter.opentable.com/gateway/proxies/restops-ap/availabilityPlanningBff/api/v2/restaurants';
        const url = `${suffix}/${socialId}/override/bulk`;
        await this._axiosInstance.put(url, openTableSpecialHours, { headers: { Cookie: cookieFromCache } });
    }

    async deleteSpecialHour({
        socialId,
        specialHourDate,
        scheduleId,
        scheduleTimestamp,
    }: {
        socialId: string;
        specialHourDate: string;
        scheduleId: string;
        scheduleTimestamp: number;
    }): Promise<void> {
        const cookieFromCache = await this._getCookieFromCache();

        const url = `https://guestcenter.opentable.com/setup/api/restaurant/${socialId}/override/${specialHourDate}`;
        await this._axiosInstance.delete(url, {
            headers: { Cookie: cookieFromCache },
            data: { id: scheduleId, timestamp: scheduleTimestamp },
        });
    }

    async addSpecialOpenHours({
        socialId,
        specialHourDate,
        body,
    }: {
        socialId: string;
        specialHourDate: string;
        body: OpenTableNewSchedule;
    }): Promise<void> {
        const cookieFromCache = await this._getCookieFromCache();

        const url = `https://guestcenter.opentable.com/setup/api/restaurant/${socialId}/override/${specialHourDate}`;
        await this._axiosInstance.put(url, body, { headers: { Cookie: cookieFromCache } });
    }

    async publishSpecialHoursUpdates({ socialId }: { socialId: string }): Promise<void> {
        const cookieFromCache = await this._getCookieFromCache();

        const url = `https://guestcenter.opentable.com/setup/api/v2/restaurant/${socialId}/publish`;
        await this._axiosInstance.post(url, null, { headers: { Cookie: cookieFromCache } });
    }

    async fetchShifts({ socialId }: { socialId: string }): Promise<OpenTableShift[]> {
        const cookieFromCache = await this._getCookieFromCache();

        const url = `https://guestcenter.opentable.com/setup/api/v2/restaurant/${socialId}/shifts`;
        const response = await this._axiosInstance.get(url, { headers: { Cookie: cookieFromCache } });

        return response.data;
    }

    async fetchSchedules({
        socialId,
        startDate,
        endDate,
    }: {
        socialId: string;
        startDate: Date;
        endDate: Date;
    }): Promise<OpenTableScheduleGetResult[]> {
        const cookieFromCache = await this._getCookieFromCache();
        const isoStartDate = DateTime.fromJSDate(startDate).toISODate();
        const isoEndDate = DateTime.fromJSDate(endDate).toISODate();

        const suffixUrl = 'https://guestcenter.opentable.com/gateway/proxies/gcAvailabilityPlanning/api/restaurants';
        const url = `${suffixUrl}/${socialId}/calendar/schedule/${isoStartDate},${isoEndDate}`;
        const response = await this._axiosInstance.get(url, { headers: { Cookie: cookieFromCache } });

        return response.data;
    }

    private async _fetchReviewsPage({
        socialId,
        page,
        cookie,
    }: {
        socialId: string;
        page: number;
        cookie: string;
    }): Promise<OpenTableReviewResponse> {
        const endDate = new Date();
        const startDate = DateTime.fromJSDate(endDate).minus({ years: 1 }).toJSDate();
        const url = `/hospitality/ot4rFeedbackService/api/v1/feedback/restaurant/${socialId}/reviews/details`;
        const body = {
            sources: ['OpenTable'],
            page,
            includeRatingOnlyReviews: true,
            startDate,
            endDate,
        };
        try {
            const response = await this._axiosInstance.post(url, body, {
                headers: {
                    Cookie: cookie,
                },
            });

            return OpenTableReviewResponseValidator.parse(response.data);
        } catch (error) {
            logger.error('[OPENTABLE_ADAPTER] - Error while getting reviews page', { err: error, page, socialId });
            throw error;
        }
    }

    private async _getCookieFromCache(): Promise<string> {
        const cookieFromCache = await this._cache.get(openTableCookieKey);
        const isCookieValid = await this._checkIfCookieIsValid(cookieFromCache);
        if (isCookieValid) {
            return cookieFromCache;
        }
        await this._setCookieInCache();
        return this._cache.get(openTableCookieKey);
    }

    private async _setCookieInCache(): Promise<string> {
        logger.info('[PUPPETEER_OPENTABLE_COOKIE] - Starting puppeteer service');
        try {
            const sandboxResponse: SandboxResponse = await this._puppeteerService.usePuppeteerSandbox(
                OPENTABLE_PUPPETEER_COOKIE_SCRAPPING_CODE
            );
            logger.info(`[PUPPETEER_OPENTABLE_COOKIE] - Response`, sandboxResponse);

            // We get the result of the puppeteer through its logs
            const result = sandboxResponse?.res;
            if (!result || result?.includes('ERROR')) {
                throw new MalouError(MalouErrorCode.CREDENTIALS_PUPPETEER_OPENTABLE_COOKIE_ERROR, {
                    message: 'Puppeteer returned an error',
                    metadata: { sandboxResponse },
                });
            }
            const otumamiauthCookie: string = JSON.parse(result)?.cookies;
            if (!otumamiauthCookie) {
                throw new MalouError(MalouErrorCode.CREDENTIALS_PUPPETEER_OPENTABLE_OTUMAMIAUTH_COOKIE_NOT_FOUND, {
                    message: '[PUPPETEER_OPENTABLE_COOKIE] Could not get otumamiauthCookie from puppeteer',
                    metadata: { sandboxResponse },
                });
            }

            // Check that the response looks like a cookie
            if (otumamiauthCookie.includes('otumamiauth')) {
                logger.info(`[PUPPETEER_OPENTABLE_COOKIE] - Got otumamiauthCookie `, { otumamiauthCookie });
                // NOT SUR IF THIS IS THE RIGHT VALUE FOR COOKIE EXPIRATION BUT FROM WHAT I SAW IT SEEMS TO BE THE RIGHT ONE
                await this._cache.set(openTableCookieKey, otumamiauthCookie, TimeInSeconds.DAY);
                return otumamiauthCookie;
            }
        } catch (err) {
            logger.warn(`[PUPPETEER_OPENTABLE_COOKIE] Failed `, { err });
        }
        throw new MalouError(MalouErrorCode.CREDENTIALS_PUPPETEER_OPENTABLE_COOKIE_ERROR, {
            message: 'Could not get cookie from puppeteer service.',
        });
    }

    private async _checkIfCookieIsValid(cookie: string | null): Promise<boolean> {
        if (!cookie) {
            return false;
        }
        try {
            // we use this endpoint as an arbitrary one to check if the cookie is valid
            const response = await this._axiosInstance.get('/rgsearch/api/v2/restaurants', {
                headers: {
                    Cookie: cookie,
                },
            });
            return response.status === 200;
        } catch (err) {
            return false;
        }
    }
}
