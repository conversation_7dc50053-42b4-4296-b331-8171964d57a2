import z from 'zod';

enum OpenTableReviewStatus {
    COMPLETE = 'Complete',
    OPEN = 'Open',
    UPDATED = 'Updated',
    NEW = 'New',
    NEW_REPLY = 'New Reply',
    HIDDEN = 'Hidden',
}

const StatusSchema = z.nativeEnum(OpenTableReviewStatus);

const OpenTableResponseSchema = z.object({
    id: z.number().nullable(),
    externalResponseId: z.string(),
    isPublished: z.boolean(),
    content: z.string(),
    isPublic: z.boolean(),
    createdAt: z.coerce.date(),
    isError: z.boolean(),
    responseFrom: z.string(),
    responseTime: z.coerce.date(),
    responseAuthorName: z.string().nullable(),
});

const OpenTableSuggestedResponsesValidator = z.object({
    id: z.number(),
    reviewId: z.string(),
    reviewTextHash: z.string(),
    generatedText: z.string(),
    generatedDate: z.coerce.date(),
});

const OpenTableReviewValidator = z.object({
    id: z.string(),
    guestName: z.string().nullable(),
    statusId: z.number(),
    statusName: z.string().nullable(),
    statuses: z.array(StatusSchema),
    reviewDate: z.coerce.date(),
    visitDate: z.coerce.date(),
    overallRating: z.number(),
    recommends: z.union([z.boolean(), z.null()]),
    foodRating: z.number(),
    serviceRating: z.number(),
    ambianceRating: z.number(),
    valueRating: z.number(),
    noiseRating: z.number(),
    noiseText: z.union([z.null(), z.string()]),
    comment: z.union([z.null(), z.string()]),
    businessResponseUrl: z.string().nullable(),
    reviewLink: z.string().nullable(),
    privateNotes: z.string().nullable(),
    rtRespondable: z.boolean().nullable(),
    respondable: z.boolean().nullable(),
    tags: z.array(z.string()),
    responses: z.array(OpenTableResponseSchema),
    updatedAt: z.union([z.coerce.date(), z.null()]),
    otReviewId: z.string(),
    suggestedResponses: z.union([z.array(OpenTableSuggestedResponsesValidator), z.null()]),
    restaurantId: z.number(),
    restaurantName: z.string(),
});

export type OpenTableReview = z.infer<typeof OpenTableReviewValidator>;

export const OpenTableReviewResponseValidator = z.object({
    meta: z.object({
        totalCount: z.number(),
        limit: z.number(),
        pageNumber: z.number(),
        hasReviews: z.boolean(),
    }),
    data: z.array(OpenTableReviewValidator),
    entitlements: z.object({
        suggestedResponsesEnabled: z.boolean(),
    }),
});

export type OpenTableReviewResponse = z.infer<typeof OpenTableReviewResponseValidator>;

const OpenTableTodayRatingReviews = z.object({
    dineDate: z.coerce.date(),
    reviewDate: z.coerce.date(),
    reviewText: z.string(),
    score: z.string(),
});

export const OpenTableTodayRatingResponseValidator = z.object({
    reviews: z.array(OpenTableTodayRatingReviews),
    summary: z.object({
        recommendRating: z.coerce.number(),
        score: z.coerce.number(),
    }),
});
