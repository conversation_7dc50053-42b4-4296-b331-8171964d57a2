import z from 'zod';

import { PartialRecord } from '@malou-io/package-utils';

import { OpenTableCountryCode, OpenTableLanguageCode, OpenTableLocale } from ':providers/opentable/interfaces/opentable.locale.interface';

const AddressSchema = z.object({
    city: z.string(),
    country: z.string(),
    countryCode: z.string(),
    postalCode: z.string().nullable(),
    province: z.string().nullable(),
    provinceCode: z.string().nullable(),
    street1: z.string().nullable(),
    street2: z.string().nullable(),
});

export const OpenTableLightRestaurantValidator = z.object({
    rid: z.number(),
    name: z.string(),
    lockoutEnabled: z.boolean(),
    billingPhoneNumber: z.string().nullable(),
    productType: z.string(),
    pendingProductType: z.string().nullable(),
    address: AddressSchema,
    locale: z.string(),
    timeZone: z.string(),
    isTestRestaurant: z.boolean(),
    timestamp: z.number(),
});

export type OpenTableLightRestaurant = z.infer<typeof OpenTableLightRestaurantValidator>;

export interface OpenTableRestaurant {
    content: OpenTableRestaurantContent;
    core: OpenTableRestaurantCore;
}

export interface OpenTableRestaurantContent {
    restaurantLocal: OpenTableRestaurantLocal;
    customMessages: OpenTableCustomMessages;
}

export type OpenTableRestaurantLocal = Record<OpenTableLocale, OpenTableRestaurantLocalizedData>;

export interface OpenTableRestaurantLocalizedData {
    address1: string;
    address2: string;
    city: string;
    state: string;
    executiveChef: string;
    crossStreet: string;
    privatePartyContact: string;
    restaurantName: string;
    sortableRestaurantName: string;
    isActive: boolean;
    banquetContact: string;
}

export interface OpenTableCustomMessages {
    RestaurantDescription: OpenTableCustomMessage;
    PublicTransit: OpenTableCustomMessage;
    Hours: OpenTableCustomMessage;
    CaterDescription?: OpenTableCustomMessage;
    CreditCardSuffix?: OpenTableCustomMessage;
    PrivatePartyLongDecription?: OpenTableCustomMessage; // Yeah there really is a typo in the OpenTable API
    LargeParty?: OpenTableCustomMessage;
    PrivatePartyDescription?: OpenTableCustomMessage;
    Confirmation?: OpenTableCustomMessage;
    ParkingDescription?: OpenTableCustomMessage;
    DetailsMessage?: OpenTableCustomMessage;
    ConfirmationDefaultMessageForCHARM?: OpenTableConfirmationDefaultMessage;
}

export interface OpenTableLocalizedMessage {
    messageSource?: string;
    version?: number;
    locked?: boolean;
    message: string;
}

export type OpenTableCustomMessage = PartialRecord<OpenTableLocale, OpenTableLocalizedMessage>;

export type OpenTableConfirmationDefaultMessage = PartialRecord<OpenTableLocale, OpenTableConfirmationMessageData>;

export interface OpenTableConfirmationMessageData {
    version: number;
    locked: boolean;
    message: string;
}

export interface OpenTableRestaurantCore {
    restaurant: OpenTableRestaurantDetails;
    paymentOptions: number[];
    foodTypes: OpenTableFoodTypes;
    messageSpecifications: OpenTableMessageSpecification[];
    restaurantFeatures: OpenTableRestaurantFeatures;
    restaurantLabels: Record<string, boolean>;
}

export interface OpenTableRestaurantDetails {
    domainName: string;
    url: string;
    facebookUrl?: string;
    twitterAccountName?: string;
    menuUrl: string;
    phoneNumber: string;
    privatePartyCapacity?: number;
    privatePartyPhone?: string;
    restaurantFax?: string;
    largePartySize?: number;
    maxAdvanceDaysId: number;
    minCCPartySize: number;
    minOnlineOptionId: number;
    minPartySize: number;
    maxLargePartyID: number;
    neighborhoodId: number;
    metroAreaId: number;
    priceBandId: number;
    restaurantId: number;
    restaurantType: string;
    sfdcId: string;
    showThirdPartyMenu: number;
    postCode: string;
    countryCode: OpenTableCountryCode;
    latitude: string;
    longitude: string;
    diningStyleId: number;
    dressCodeId: number;
    smokingId: number;
    acceptsWalkins: boolean;
    restaurantStateId: number;
    hasPrivateParty: boolean;
    enablePrivateDining: boolean;
    publishPrivateDining: boolean;
    privateDiningMenu?: string;
    privatePartyEmail?: string;
    primaryLanguage: OpenTableLanguageCode;
    ccAccountStatusID: number;
    ccMerchantID?: string;
    ccUserID?: string;
    acceptLargeParty: boolean;
    romsModifiedDTUtc?: string;
    currencyCode: string;
    timezoneID: number;
    adWordsID?: string;
    adWordsValue?: string;
}

export interface OpenTableFoodTypes {
    primaryCuisineId: string;
    otherCuisines: string[];
}

export interface OpenTableMessageSpecification {
    version?: number;
    createDateTime?: string;
    lastUpdatedDateTime?: string;
    Type: string;
    Specification: OpenTableSpecification;
}

export interface OpenTableSpecification {
    specification: OpenTableSpecificationDetail[];
    schemaVersion: number;
}

export enum OpenTableDay {
    MONDAY = 'mon',
    TUESDAY = 'tue',
    WEDNESDAY = 'wed',
    THURSDAY = 'thu',
    FRIDAY = 'fri',
    SATURDAY = 'sat',
    SUNDAY = 'sun',
}

export interface OpenTableSpecificationDetail {
    days: OpenTableDay[];
    shiftStarts: string;
    shiftEnds: string;
    standardShiftName: OpenTableStandardShiftName;
}

export interface OpenTableStandardShiftName {
    id: string;
}

export interface OpenTableRestaurantFeatures {
    ContentPartner?: OpenTableContentPartner;
    DirectMessaging?: OpenTableDirectMessagingFeature;
    Experience?: OpenTableExperienceFeature;
    Gifts?: OpenTableGiftsFeature;
    Operations?: OpenTableOperationsFeature;
    Partner?: OpenTablePartnerFeature;
    Payments?: OpenTablePaymentsFeature;
    Pickup?: OpenTablePickupFeature;
    Profile?: OpenTableProfileFeature;
    ProfileUpdateTimes?: OpenTableProfileUpdateTimes;
    PromotedInventory?: OpenTablePromotedInventory;
    Reservation?: OpenTableReservationFeature;
    SafetyPrecautions?: OpenTableSafetyPrecautions;
    TableCategories?: OpenTableTableCategories;
    Ticketing?: OpenTableTicketingFeature;
    Waitlist?: OpenTableWaitlistFeature;
}

export interface OpenTableContentPartner {
    Name: string;
}

export interface OpenTableDirectMessagingFeature {
    DirectMessagingEnabled: boolean;
}

export interface OpenTableExperienceFeature {
    HasExperiences: boolean;
}

export interface OpenTableGiftsFeature {
    GiftsURLs: string;
    GiftsEnabled: boolean;
}

export interface OpenTableOperationsFeature {
    PermanentlyClosed: boolean;
}

export interface OpenTablePartnerFeature {
    FeedExcludeGoogle: boolean;
}

export interface OpenTablePaymentsFeature {
    StrongCustomerAuthentication: boolean;
}

export interface OpenTablePickupFeature {
    TipsEnabled: boolean;
    PickupEnabled: boolean;
    AcceptingPickup: boolean;
}

export interface OpenTableProfileFeature {
    DeliveryDirectViaPhoneOnly: boolean;
    DinerAIEnabled: boolean;
}

export interface OpenTableProfileUpdateTimes {
    Description: string;
    ContactCuisinePrice: string;
    Details: string;
    BusinessHours: string;
    Instructions: string;
    DiningExperience: string;
    Offerings: string;
}

export interface OpenTablePromotedInventory {
    HasActiveCampaign: boolean;
    BaseCampaign: number;
    TargetSet: string;
    HasCustomPromotion: boolean;
    HasPromotedResults: boolean;
    AutoFlexCampaign: string;
    InFlexiblePricingProgram: boolean;
    InPaidSearchProgram: boolean;
    HasOnlyHiddenCampaign: boolean;
    HasPaidSearch: boolean;
}

export interface OpenTableReservationFeature {
    ReservationMaxPartySize: number;
    ReservationMinPartySize: number;
    ReservationPartySizeSetByGC: boolean;
}

export interface OpenTableSafetyPrecautions {
    CommonAreaDistancing: boolean;
    SanitizedSurfaces: boolean;
    CommonAreaCleaning: boolean;
    RequireWaitstaffMasks: boolean;
    CleanMenus: boolean;
    RequireDinerMasks: boolean;
    TableLayoutWithExtraSpace: boolean;
    ContactlessPayment: boolean;
    LimitedSeating: boolean;
    ProofOfVaccinationRequired: boolean;
    ProhibitSickStaff: boolean;
    SanitizerProvidedForCustomers: boolean;
}

export interface OpenTableTableCategories {
    outdoor: boolean;
    bar: boolean;
    highTop: boolean;
    counter: boolean;
}

export interface OpenTableTicketingFeature {
    TicketingEnabled: boolean;
}

export interface OpenTableWaitlistFeature {
    WaitlistV2Enabled: boolean;
    WaitlistV2Only: boolean;
}

export interface OpentableSpecialHours {
    upsertOverrideRequestDTOs: UpsertOverrideRequestDto[];
    deleteOverrideRequestDTOs: unknown[];
}

export interface UpsertOverrideRequestDto {
    id: unknown | null;
    name: string;
    closed: boolean;
    shifts: unknown[];
    isHoliday: boolean;
    cancelReservationsOnClosure: boolean;
    reservationCancellationMessage: unknown | null;
    closedDayMessage: unknown | null;
    date: string;
}
