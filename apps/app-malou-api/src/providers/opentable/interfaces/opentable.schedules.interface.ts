import { OpenTableShift, OpenTableShiftDays } from ':providers/opentable/interfaces/opentable.shifts.interface';

export interface OpenTableSchedule {
    id: string | null;
    rid: number;
    name: unknown | null;
    closed: boolean;
    cancelReservationsOnClosure: boolean;
    reservationCancellationMessage: unknown | null;
    closedDayMessage: unknown | null;
    date: string;
    isHoliday: boolean;
    timestamp: number;
}

export interface OpenTableShiftView {
    name: string;
    firstSeating: number;
    lastSeating: number;
    isCustomShift: boolean;
    recurringShiftDays: OpenTableShiftDays | null;
}

export type OpenTableScheduleGetResult = OpenTableSchedule & {
    isDefaultSchedule: boolean;
    shiftNames: string[];
    shiftViews: OpenTableShiftView[];
};

export type OpenTableNewSchedule = OpenTableSchedule & {
    errorCodes: unknown | null;
    creationDate: string;
    createdBy: unknown | null;
    lastUpdatedDate: string;
    lastUpdatedBy: unknown | null;
    dayOfWeek: number;
    shifts: OpenTableScheduleShift[];
};

export interface OpenTableScheduleShift {
    masterShiftId?: string;
    masterShift?: OpenTableShift;
    customShiftId?: unknown | null;
    customShift: OpenTableShift | null;
    shiftType: OpenTableScheduleShiftType;
    tempId?: string;
}

export enum OpenTableScheduleShiftType {
    UNCHANGED = 'unchanged',
    NEW = 'new',
}
