export interface OpenTableShift {
    errorCodes: unknown | null;
    creationDate: string;
    createdBy: string;
    lastUpdatedDate: string;
    lastUpdatedBy: string;
    rid: number;
    shiftDays: OpenTableShiftDays;
    name: string;
    firstSeating: number;
    lastSeating: number;
    floorPlans: string[];
    turnTimes: OpenTableTurnTime[];
    partySizeTurnControls: unknown[];
    tableBasedTurnControls: unknown[];
    turnControlsExpirationPolicy: OpenTableTurnControlsExpirationPolicy;
    defaultPacingLimit: number;
    pacingLimits: OpenTablePacingLimit[];
    timePeriodRatio: number[];
    subShifts: OpenTableSubShift[];
    color: number;
    onlineWaitlist: OpenTableOnlineWaitlist;
    maxCapacity: number;
    aLaCarteUnavailable: boolean;
    coverRestrictions: unknown | null;
    partySizeRestrictions: unknown | null;
    hasBeenOptimized: boolean;
    largePartyInTakeInterval: unknown | null;
    largePartyMaxConcurrent: unknown | null;
    id: string;
    timestamp: number;
    seatingPlans: OpenTableSeatingPlan[];
    configurationTargets: unknown | null;
}

export enum OpenTableDay {
    SUNDAY = 'sunday',
    MONDAY = 'monday',
    TUESDAY = 'tuesday',
    WEDNESDAY = 'wednesday',
    THURSDAY = 'thursday',
    FRIDAY = 'friday',
    SATURDAY = 'saturday',
}

export type OpenTableShiftDays = Record<OpenTableDay, boolean>;

export interface OpenTableTurnTime {
    configurationTargetId: unknown | null;
    partySize: number;
    value: number;
}

export interface OpenTableTurnControlsExpirationPolicy {
    expirationTime: string;
}

export interface OpenTablePacingLimit {
    time: number;
    value: number;
    isCustom: boolean;
}

export interface OpenTableSubShift {
    subShiftId: string;
    start: number;
    end: number;
    defaultPacingLimit: number;
    turnTimes: OpenTableTurnTime[];
    partySizeAvailability: OpenTablePartySizeAvailability;
    tableAvailability: OpenTableTableAvailability;
    partySizePacingLimits: unknown[];
    targetedPacingLimits: unknown | null;
}

export interface OpenTablePartySizeAvailability {
    largePartiesReservable: boolean;
}

export interface OpenTableTableAvailability {
    largeCombinations: OpenTableLargeCombinations;
    largeStandaloneTables: unknown | null;
    standardRules: OpenTableStandardRule[];
    advancedRules: OpenTableAdvancedRule[];
}

export interface OpenTableLargeCombinations {
    reservable: boolean;
    bookingChannels: OpenTableBookingChannels;
    matchedIds: string[];
}

export interface OpenTableBookingChannels {
    online: boolean;
    inHouse: boolean;
    walkIn: boolean;
}

export interface OpenTableStandardRule {
    reservable: boolean;
    bookingChannels: OpenTableBookingChannels;
    category: string;
    floorPlanId: unknown | null;
    matchedTables: string[];
    matchedCombinations: string[];
}

export interface OpenTableAdvancedRule {
    reservable: boolean;
    bookingChannels: OpenTableBookingChannels;
    category: unknown | null;
    floorPlanId: unknown | null;
    tableIds: string[];
    tableCombinations: string[];
}

export interface OpenTableOnlineWaitlist {
    enabled: boolean;
    timePeriods: OpenTableTimePeriod[];
}

export interface OpenTableTimePeriod {
    enabled: boolean;
    start: number;
    end: number;
}

export interface OpenTableSeatingPlan {
    floorPlanId: string;
    maxCapacity: number;
}
