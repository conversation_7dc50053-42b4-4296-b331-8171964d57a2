import 'reflect-metadata';

import ':env';

import { promises as fs } from 'fs';
import path from 'path';
import { container, singleton } from 'tsyringe';

import { GoogleSheetsService } from ':services/google-sheets/google-sheets.service';

@singleton()
export class AddMappingForPlatformCategoriesTask {
    private readonly _GSHEET_ID = '1CS5UM0jTy9AehgiGHHFfn2OmsL85frJpcaRFjy_9en8';

    constructor(private readonly _googleSheetsService: GoogleSheetsService) {}

    async execute() {
        const googleSheet = await this._googleSheetsService.loadGoogleSheet(this._GSHEET_ID);
        const workSheet = googleSheet.sheetsByTitle['categories'];
        const categoriesRows = await workSheet.getRows();

        let tripadvisorMapping = `export const gmbCategoriesToTripadvisorCategories: Partial<Record<GmbCategoryIdEnum, string>> = {`;
        let resyMapping = `export const gmbCategoriesToResyCategories: Partial<Record<GmbCategoryIdEnum, string>> = {`;
        let opentableMapping = `export const gmbCategoriesToOpenTableCategories: Partial<Record<GmbCategoryIdEnum, string>> = {`;

        categoriesRows.forEach((row) => {
            if (row['GBP_ID'] && row['TripAdvisor']) {
                tripadvisorMapping += `
                    [GmbCategoryIdEnum.${row['GBP_ID'].trim().replace('gcid:', '').toUpperCase()}]: '${row['TripAdvisor'].trim()}',`;
            }

            if (row['GBP_ID'] && row['Resy']) {
                resyMapping += `
                    [GmbCategoryIdEnum.${row['GBP_ID'].trim().replace('gcid:', '').toUpperCase()}]: '${row['Resy'].trim()}',`;
            }

            if (row['GBP_ID'] && row['OpenTable']) {
                opentableMapping += `
                    [GmbCategoryIdEnum.${row['GBP_ID'].trim().replace('gcid:', '').toUpperCase()}]: '${row['OpenTable'].trim()}',`;
            }
        });

        tripadvisorMapping += '}';
        resyMapping += '}';
        opentableMapping += '}';

        const gmbToTripadvisorFilePath = path.join(__dirname, 'gmb-to-tripadvisor.mapping.ts');
        const gmbToResyFilePath = path.join(__dirname, 'gmb-to-resy.mapping.ts');
        const gmbToOpenTableFilePath = path.join(__dirname, 'gmb-to-opentable.mapping.ts');
        await fs.writeFile(gmbToTripadvisorFilePath, tripadvisorMapping);
        await fs.writeFile(gmbToResyFilePath, resyMapping);
        await fs.writeFile(gmbToOpenTableFilePath, opentableMapping);
    }
}

const task = container.resolve(AddMappingForPlatformCategoriesTask);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
