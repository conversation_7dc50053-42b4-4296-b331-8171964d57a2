import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { IRestaurant } from '@malou-io/package-models';
import { cleanUtmParamsFromUrl, includesUtmParams } from '@malou-io/package-utils';

import { InformationUpdatesDtoMapper } from ':modules/information-updates/information-updates.dto-mapper';
import { InformationUpdatesUseCases } from ':modules/information-updates/information-updates.use-cases';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ':plugins/db';

interface UtmUpdate {
    website?: string;
    reservationUrl?: string;
    orderUrl?: string;
    menuUrl?: string;
}

@singleton()
class RemoveUtmInLinksTask {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _informationUpdatesUseCases: InformationUpdatesUseCases,
        private readonly _informationUpdatesDtoMapper: InformationUpdatesDtoMapper
    ) {}

    async execute() {
        const restaurants = await this._findRestaurantsWithUtmParams();
        console.log(`Found ${restaurants.length} restaurants to update`);
        for (const restaurant of restaurants) {
            const update = this._getCleanUpdateForRestaurant(restaurant);
            await this._saveAndCreateUpdate(restaurant, update);
        }
    }

    private async _findRestaurantsWithUtmParams(): Promise<IRestaurant[]> {
        return this._restaurantsRepository.find({
            filter: {
                active: true,
                $or: [{ website: /utm_/ }, { reservationUrl: /utm_/ }, { orderUrl: /utm_/ }, { menuUrl: /utm_/ }],
            },
            options: {
                lean: true,
            },
        });
    }

    private _getCleanUpdateForRestaurant(restaurant: IRestaurant): UtmUpdate {
        const update: UtmUpdate = {};
        if (restaurant.website && includesUtmParams(restaurant.website)) {
            update.website = cleanUtmParamsFromUrl(restaurant.website);
        }
        if (restaurant.reservationUrl && includesUtmParams(restaurant.reservationUrl)) {
            update.reservationUrl = cleanUtmParamsFromUrl(restaurant.reservationUrl);
        }
        if (restaurant.orderUrl && includesUtmParams(restaurant.orderUrl)) {
            update.orderUrl = cleanUtmParamsFromUrl(restaurant.orderUrl);
        }
        if (restaurant.menuUrl && includesUtmParams(restaurant.menuUrl)) {
            update.menuUrl = cleanUtmParamsFromUrl(restaurant.menuUrl);
        }
        return update;
    }

    private async _saveAndCreateUpdate(restaurant: IRestaurant, update: UtmUpdate): Promise<void> {
        if (Object.keys(update).length === 0) {
            console.log(`Restaurant ${restaurant._id} has no updates to apply`);
            return;
        }
        await this._restaurantsRepository.updateOne({
            filter: { _id: restaurant._id },
            update: { $set: update },
        });
        console.log(`Restaurant ${restaurant._id} updated`);
        const previousDate = this._informationUpdatesDtoMapper.toIInformationUpdateData({
            ...restaurant,
            openingDate: restaurant.openingDate?.toISOString(),
        });
        await this._informationUpdatesUseCases.saveInformationUpdateData(restaurant._id.toString(), update, previousDate);
        console.log(`Information update created for restaurant ${restaurant._id}`);
    }
}

const task = container.resolve(RemoveUtmInLinksTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
