import 'reflect-metadata';

import ':env';

import { chunk, omit } from 'lodash';
import { autoInjectable, container } from 'tsyringe';

import { PlatformKey, TimeInMilliseconds, waitFor } from '@malou-io/package-utils';

import { PublishOnGmbPlatformUseCase } from ':modules/platforms/use-cases/publish-on-connected-platforms/platforms/publish-on-gmb-platform/publish-on-gmb-platform.use-case';
import { RestaurantPopulatedToPublish } from ':modules/platforms/use-cases/publish-on-connected-platforms/publish-on-connected-platforms.use-case';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ':plugins/db';

@autoInjectable()
class GmbUpdateValidateOnlyTask {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _publishOnGmbPlatformUseCase: PublishOnGmbPlatformUseCase
    ) {}

    async execute() {
        const restaurants: RestaurantPopulatedToPublish[] = (await this._restaurantsRepository.find({
            filter: { active: true, access: { $elemMatch: { platformKey: PlatformKey.GMB } } },
            options: {
                lean: true,
                populate: [
                    { path: 'category' },
                    { path: 'logo' },
                    { path: 'cover' },
                    { path: 'categoryList' },
                    { path: 'attributeList', populate: [{ path: 'attribute' }] },
                ],
            },
        })) as RestaurantPopulatedToPublish[];

        let successCount = 0;
        let errorCount = 0;

        console.log('Number of restaurants to update:', restaurants.length);

        const promises = restaurants.map(async (restaurant) => {
            const validateOnly = true;
            const updateData = this._cleanRestaurantUpdateDate(restaurant);
            const gmbPublished = await this._publishOnGmbPlatformUseCase.execute({
                restaurant: updateData,
                keysToUpdate: Object.keys(updateData) as (keyof RestaurantPopulatedToPublish)[],
                options: { validateOnly },
            });
            if (!gmbPublished.success) {
                console.error('Failed to publish on GMB', gmbPublished);
                errorCount++;
            } else {
                console.log('Success!');
                successCount++;
            }
        });

        const CHUNK_SIZE = 10;
        const chunkedPromises = chunk(promises, CHUNK_SIZE);
        const chunkCount = chunkedPromises.length;
        let currentCount = 1;

        for (const updateGmbPromises of chunkedPromises) {
            await Promise.all(updateGmbPromises);
            if (chunkCount > currentCount) {
                const TEN_SECONDES = 10 * TimeInMilliseconds.SECOND;
                await waitFor(TEN_SECONDES);
            }
            currentCount++;
        }

        console.log('Success:', successCount);
        console.log('Error:', errorCount);
    }

    private _cleanRestaurantUpdateDate(restaurant: RestaurantPopulatedToPublish): RestaurantPopulatedToPublish {
        return omit(restaurant, ['latlng']);
    }
}

const task = container.resolve(GmbUpdateValidateOnlyTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
