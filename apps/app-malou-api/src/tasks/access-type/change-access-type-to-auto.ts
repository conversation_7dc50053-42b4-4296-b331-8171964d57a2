import ':env';

import { IRestaurant, RestaurantModel } from '@malou-io/package-models';
import { PlatformAccessType, PlatformKey } from '@malou-io/package-utils';

import ':plugins/db';

import prompts = require('prompts');

async function main() {
    const filters = {
        access: {
            $elemMatch: {
                active: true,
                platformKey: { $in: [PlatformKey.OPENTABLE, PlatformKey.ZENCHEF] },
                accessType: { $ne: PlatformAccessType.AUTO },
            },
        },
    };

    const restaurants: IRestaurant[] = await RestaurantModel.find(filters);

    const response = await prompts({
        type: 'confirm',
        name: 'value',
        message: `About to update ${restaurants.length} restaurants`,
        initial: false,
    });

    if (!response.value) {
        console.log('exit...');
        process.exit(0);
    }

    await RestaurantModel.updateMany(filters, {
        'access.$.accessType': PlatformAccessType.AUTO,
    });
}

main()
    .then(() => process.exit(0))
    .catch((e) => {
        console.error('e :>> ', e);
        process.exit(1);
    });
