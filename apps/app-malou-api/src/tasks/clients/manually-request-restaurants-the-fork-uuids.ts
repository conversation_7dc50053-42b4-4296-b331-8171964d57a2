import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { TheForkService } from ':modules/clients/provider-clients/providers/thefork/service/thefork.service';
import ':plugins/db';

@singleton()
class ManuallyRequestRestaurantsTheForkUuidsTask {
    constructor(private readonly _theForkService: TheForkService) {}

    async execute(): Promise<void> {
        console.log('Requesting restaurants TheFork uuids');

        const restaurantIds: string[] = [];

        for (const restaurantId of restaurantIds) {
            console.log('Requesting restaurant ' + restaurantId);
            await this._theForkService.requestRestaurantTheForkUuids(restaurantId);
        }
        console.log('Done');
    }
}

const task = container.resolve(ManuallyRequestRestaurantsTheForkUuidsTask);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
