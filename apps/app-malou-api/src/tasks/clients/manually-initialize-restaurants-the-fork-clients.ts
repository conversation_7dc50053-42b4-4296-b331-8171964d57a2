import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { InitializeTheForkClientsUseCase } from ':modules/clients/provider-clients/providers/thefork/use-cases/initialize-the-fork-clients/initialize-the-fork-clients.use-case';
import ':plugins/db';

@singleton()
class ManuallyInitializeRestaurantsTheForkClientsTask {
    constructor(private readonly _initializeTheForkClientsUseCase: InitializeTheForkClientsUseCase) {}

    async execute(): Promise<void> {
        await this._initializeTheForkClientsUseCase.execute();
    }
}

const task = container.resolve(ManuallyInitializeRestaurantsTheForkClientsTask);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
