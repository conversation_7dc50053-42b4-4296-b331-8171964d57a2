import { uniq } from 'lodash';
import { injectable } from 'tsyringe';

import { IRestaurant } from '@malou-io/package-models';
import {
    configInformationUpdateSupportedKeys,
    MalouErrorCode,
    PlatformKey,
    platformKeyToProvider,
    YextPublisherId,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { YextGetEntitiesService } from ':modules/publishers/yext/services/yext-get-entities.service';
import { YextProvider } from ':providers/yext/yext.provider';
import { UnsupportedYextPublisherId, YextListing, YextListingStatus } from ':providers/yext/yext.provider.interfaces';
import { SlackChannel, SlackService } from ':services/slack.service';

@injectable()
export default class YextListingService {
    constructor(
        private readonly _yextProvider: YextProvider,
        private readonly _slackService: SlackService,
        private readonly _yextGetEntitiesService: YextGetEntitiesService
    ) {}

    async getListingsForRestaurant(restaurantId: string): Promise<YextListing[]> {
        const { yextAccount, yextLocation } = await this._yextGetEntitiesService.execute({ restaurantId });

        const listingResponse = await this._yextProvider.getListingsForLocation(yextAccount.partnerAccountId, [
            yextLocation.partnerLocationId,
        ]);

        const unhandledPublishers = this._getUnhandledPublishers(listingResponse.response.listings);

        if (unhandledPublishers.length > 0) {
            logger.error('Unhandled Yext publishers', {
                unhandledPublishers,
            });

            // TODO: uncomment when list of publishers in app is stabilized
            // const unhandledPublisherIds = unhandledPublishers.map((publisher) => publisher.publisherId);
            // await this._sendAlert(unhandledPublisherIds, restaurant, yextLocation.partnerLocationId);
        }

        return listingResponse.response.listings
            .filter((publisher) => ![YextListingStatus.OPTED_OUT, YextListingStatus.UNAVAILABLE].includes(publisher.status))
            .filter((publisher) => !Object.values(UnsupportedYextPublisherId).includes(publisher.publisherId as UnsupportedYextPublisherId))
            .filter((publisher) => configInformationUpdateSupportedKeys[publisher.publisherId as YextPublisherId]?.name);
    }

    async isPlatformKeyHandledByYext({ restaurantId, platformKey }: { restaurantId: string; platformKey: PlatformKey }): Promise<boolean> {
        try {
            const yextListing = await this.getListingsForRestaurant(restaurantId);

            const key = configInformationUpdateSupportedKeys[platformKey].name;
            const providerKey = platformKeyToProvider['YEXT'][key] as YextPublisherId;

            return yextListing.some((platform) => platform.publisherId === providerKey);
        } catch (e) {
            return false;
        }
    }

    async optOutListings({ restaurantId, publisherIds }: { restaurantId: string; publisherIds: YextPublisherId[] }): Promise<void> {
        try {
            const { yextAccount, yextLocation } = await this._yextGetEntitiesService.execute({
                restaurantId,
            });

            const yextListings = await this.getListingsForRestaurant(restaurantId);

            const publisherIdsToOptOut = uniq(
                yextListings
                    .filter((listing) => publisherIds.includes(listing.publisherId as YextPublisherId))
                    .filter((listing) => listing.status !== YextListingStatus.OPTED_OUT)
                    .map((listing) => listing.publisherId as YextPublisherId)
            );
            await this._yextProvider.optOutListing(yextAccount.partnerAccountId, [yextLocation.partnerLocationId], publisherIdsToOptOut);
        } catch (error) {
            logger.error('[YEXT] Error opting out Yext listings', {
                restaurantId,
                error,
            });
            return;
        }
    }

    async optInListings({ restaurantId, publisherIds }: { restaurantId: string; publisherIds: YextPublisherId[] }): Promise<void> {
        try {
            const { yextAccount, yextLocation } = await this._yextGetEntitiesService.execute({
                restaurantId,
            });

            const yextListings = await this.getListingsForRestaurant(restaurantId);

            const publisherIdsToOptIn = uniq(
                yextListings
                    .filter((listing) => publisherIds.includes(listing.publisherId as YextPublisherId))
                    .filter((listing) => listing.status === YextListingStatus.OPTED_OUT)
                    .map((listing) => listing.publisherId as YextPublisherId)
            );
            await this._yextProvider.optInListing(yextAccount.partnerAccountId, [yextLocation.partnerLocationId], publisherIdsToOptIn);
        } catch (error) {
            logger.error('[YEXT] Error opting in Yext listings', {
                restaurantId,
                error,
            });
            return;
        }
    }

    private _getUnhandledPublishers(publishers: YextListing[]): YextListing[] {
        const handledPublishers = Object.values(YextPublisherId);

        const notHandledPublishers = publishers.filter((publisher) => {
            const isHandledPublisher = handledPublishers.includes(publisher.publisherId as YextPublisherId);
            const isUnsupportedPublisher = Object.values(UnsupportedYextPublisherId).includes(
                publisher.publisherId as UnsupportedYextPublisherId
            );

            return !isHandledPublisher && !isUnsupportedPublisher;
        });

        return notHandledPublishers;
    }

    private async _sendAlert(
        unhandledPublisherIds: string[],
        restaurant: Pick<IRestaurant, 'name' | '_id'>,
        locationId: string
    ): Promise<void> {
        this._slackService.sendAlert({
            channel: SlackChannel.PLATFORM_UPDATES_ALERTS,
            data: {
                err: new MalouError(MalouErrorCode.UNHANDLED_YEXT_PUBLISHERS, {
                    message: 'Unhandled Yext publishers',
                    metadata: { unhandledPublisherIds, restaurantName: restaurant.name, restaurantId: restaurant._id },
                }),
                restaurantId: restaurant._id.toString(),
                endpoint: `publishers/yext/restaurants/${restaurant._id}/listings?locationIds=${[locationId]}`,
            },
        });
    }
}
