import { Day } from '@malou-io/package-utils';

import { Shift } from ':modules/shifts/shift';
import { getDefaultShift } from ':modules/shifts/tests/shift.builder';

describe('Shift', () => {
    describe('fromOpenTable', () => {
        it('should return a new Shift with not round minutes', async () => {
            const openTableShift = getDefaultShift()
                .name('OpenTable Shift')
                .firstSeating(735)
                .lastSeating(825)
                .shiftDays({
                    monday: false,
                    tuesday: false,
                    wednesday: false,
                    thursday: false,
                    friday: false,
                    saturday: true,
                    sunday: true,
                })
                .build();

            const result = Shift.fromOpenTable(openTableShift);

            expect(result).toEqual(
                new Shift({
                    name: 'OpenTable Shift',
                    startTime: '12:15',
                    endTime: '13:45',
                    days: [Day.SATURDAY, Day.SUNDAY],
                })
            );
        });

        it('should return a new Shift with not round minutes', async () => {
            const openTableShift = getDefaultShift()
                .name('OpenTable Shift')
                .firstSeating(540)
                .lastSeating(660)
                .shiftDays({
                    monday: false,
                    tuesday: false,
                    wednesday: false,
                    thursday: false,
                    friday: false,
                    saturday: true,
                    sunday: true,
                })
                .build();

            const result = Shift.fromOpenTable(openTableShift);

            expect(result).toEqual(
                new Shift({
                    name: 'OpenTable Shift',
                    startTime: '09:00',
                    endTime: '11:00',
                    days: [Day.SATURDAY, Day.SUNDAY],
                })
            );
        });
    });
});
