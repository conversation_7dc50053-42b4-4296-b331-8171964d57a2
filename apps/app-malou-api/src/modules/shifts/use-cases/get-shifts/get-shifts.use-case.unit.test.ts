import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { OPENTABLE_SHIFTS_EXAMPLE } from ':modules/shifts/tests/opentable-shift.example';
import { GetShiftsUseCase } from ':modules/shifts/use-cases/get-shifts/get-shifts.use-case';
import { OpenTableProvider } from ':providers/opentable/opentable.provider';

let getShiftsUseCase: GetShiftsUseCase;

describe('', () => {
    beforeAll(() => {
        container.clearInstances();

        registerRepositories(['PlatformsRepository']);
        const openTableProviderStub = {
            fetchShifts: jest.fn().mockResolvedValue(OPENTABLE_SHIFTS_EXAMPLE),
        } as unknown as OpenTableProvider;

        container.registerInstance(OpenTableProvider, openTableProviderStub);

        getShiftsUseCase = container.resolve(GetShiftsUseCase);
    });

    describe('execute', () => {
        it('should return empty array if no opentable platforms found', async () => {
            const restaurantId = newDbId();
            const platformKey = PlatformKey.GMB;
            const testCase = new TestCaseBuilderV2<'platforms'>({
                seeds: {
                    platforms: {
                        data() {
                            return [getDefaultPlatform().name('platform_0').restaurantId(restaurantId).key(platformKey).build()];
                        },
                    },
                },
                expectedResult(_dependencies): any {
                    return [];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            const result = await getShiftsUseCase.execute(restaurantId.toString(), PlatformKey.OPENTABLE);
            expect(result).toEqual(expectedResult);
        });

        it('should return shifts for opentable platform', async () => {
            const restaurantId = newDbId();
            const platformKey = PlatformKey.OPENTABLE;
            const testCase = new TestCaseBuilderV2<'platforms'>({
                seeds: {
                    platforms: {
                        data() {
                            return [getDefaultPlatform().name('platform_0').restaurantId(restaurantId).key(platformKey).build()];
                        },
                    },
                },
                expectedResult(_dependencies): any {
                    return [
                        {
                            days: ['TUESDAY', 'WEDNESDAY'],
                            endTime: '22:30',
                            name: 'Mar-Mer',
                            startTime: '19:00',
                        },
                        {
                            days: ['MONDAY', 'SUNDAY'],
                            endTime: '22:15',
                            name: 'Dim-Lun',
                            startTime: '19:00',
                        },
                        {
                            days: ['THURSDAY', 'FRIDAY', 'SATURDAY'],
                            endTime: '22:30',
                            name: 'Jeu-Sam',
                            startTime: '19:00',
                        },
                    ];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            const result = await getShiftsUseCase.execute(restaurantId.toString(), platformKey);
            expect(result).toEqual(expectedResult);
        });
    });
});
