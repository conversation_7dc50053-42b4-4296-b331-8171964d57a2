import { singleton } from 'tsyringe';

import { ShiftDto } from '@malou-io/package-dto';
import { PlatformKey } from '@malou-io/package-utils';

import PlatformsRepository from ':modules/platforms/platforms.repository';
import { Shift } from ':modules/shifts/shift';
import { OpenTableProvider } from ':providers/opentable/opentable.provider';

@singleton()
export class GetShiftsUseCase {
    PLATFORMS_WITH_SHIFTS = [PlatformKey.OPENTABLE];

    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _openTableProvider: OpenTableProvider
    ) {}

    async execute(restaurantId: string, platformKey: PlatformKey): Promise<ShiftDto[]> {
        if (!this.PLATFORMS_WITH_SHIFTS.includes(platformKey)) {
            return [];
        }
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, platformKey);
        if (!platform || !platform.socialId) {
            return [];
        }
        const shifts = await this._openTableProvider.fetchShifts({ socialId: platform.socialId });
        return shifts.map((shift) => Shift.fromOpenTable(shift).toDto());
    }
}
