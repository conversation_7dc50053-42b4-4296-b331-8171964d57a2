import { NextFunction, Request, Response } from 'express';
import { autoInjectable } from 'tsyringe';

import { GetShiftsParamsDto, getShiftsParamsValidator } from '@malou-io/package-dto';
import { ApiResultError, ApiResultV2 } from '@malou-io/package-utils';

import { Params } from ':helpers/decorators/validators';
import { GetShiftsUseCase } from ':modules/shifts/use-cases/get-shifts/get-shifts.use-case';

@autoInjectable()
export default class ShiftsController {
    constructor(private readonly _getShiftsUseCase: GetShiftsUseCase) {}

    @Params(getShiftsParamsValidator)
    async handleGetShifts(req: Request<GetShiftsParamsDto>, res: Response<ApiResultV2<any, ApiResultError>>, next: NextFunction) {
        try {
            const { platformKey, restaurantId } = req.params;

            const result = await this._getShiftsUseCase.execute(restaurantId, platformKey);
            return res.json({
                data: result,
            });
        } catch (err) {
            next(err);
        }
    }
}
