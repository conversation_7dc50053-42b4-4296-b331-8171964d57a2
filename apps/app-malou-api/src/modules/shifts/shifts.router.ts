import { Router } from 'express';
import { autoInjectable } from 'tsyringe';

import ShiftsController from ':modules/shifts/shifts.controller';
import { authorize } from ':plugins/passport';

@autoInjectable()
export default class ShiftsRouter {
    constructor(private _shiftsController: ShiftsController) {}

    init(router: Router): void {
        router.get('/shifts/:platform_key/restaurants/:restaurant_id', authorize(), (req, res, next) =>
            this._shiftsController.handleGetShifts(req, res, next)
        );
    }
}
