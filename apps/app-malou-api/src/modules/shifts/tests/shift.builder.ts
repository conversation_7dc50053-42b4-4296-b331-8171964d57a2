import { Builder } from 'builder-pattern';

import { OpenTableShift } from ':providers/opentable/interfaces/opentable.shifts.interface';

type OpenTableShiftPayload = OpenTableShift;

const _buildShift = (shift: OpenTableShiftPayload) => Builder<OpenTableShiftPayload>(shift);

export const getDefaultShift = () =>
    _buildShift({
        errorCodes: null,
        creationDate: '0001-01-01T00:00:00Z',
        createdBy: '',
        lastUpdatedDate: new Date().toISOString(),
        lastUpdatedBy: '',
        rid: 390150,
        shiftDays: {
            sunday: false,
            monday: true,
            tuesday: false,
            wednesday: false,
            thursday: false,
            friday: false,
            saturday: false,
        },
        name: 'shift name',
        firstSeating: 1100,
        lastSeating: 1400,
        floorPlans: [],
        turnTimes: [
            {
                configurationTargetId: null,
                partySize: 1,
                value: 120,
            },
            {
                configurationTargetId: null,
                partySize: 2,
                value: 120,
            },
            {
                configurationTargetId: null,
                partySize: 3,
                value: 120,
            },
            {
                configurationTargetId: null,
                partySize: 4,
                value: 120,
            },
            {
                configurationTargetId: null,
                partySize: 5,
                value: 150,
            },
            {
                configurationTargetId: null,
                partySize: 6,
                value: 150,
            },
            {
                configurationTargetId: null,
                partySize: 7,
                value: 150,
            },
            {
                configurationTargetId: null,
                partySize: 8,
                value: 150,
            },
            {
                configurationTargetId: null,
                partySize: 9,
                value: 150,
            },
            {
                configurationTargetId: null,
                partySize: 10,
                value: 150,
            },
            {
                configurationTargetId: null,
                partySize: 11,
                value: 150,
            },
            {
                configurationTargetId: null,
                partySize: 12,
                value: 150,
            },
        ],
        partySizeTurnControls: [],
        tableBasedTurnControls: [],
        turnControlsExpirationPolicy: {
            expirationTime: 'DoNotRelease',
        },
        defaultPacingLimit: 30,
        pacingLimits: [
            {
                time: 1140,
                value: 16,
                isCustom: true,
            },
            {
                time: 1155,
                value: 14,
                isCustom: true,
            },
            {
                time: 1170,
                value: 12,
                isCustom: true,
            },
            {
                time: 1185,
                value: 10,
                isCustom: true,
            },
            {
                time: 1200,
                value: 0,
                isCustom: false,
            },
            {
                time: 1215,
                value: 0,
                isCustom: false,
            },
            {
                time: 1230,
                value: 0,
                isCustom: false,
            },
            {
                time: 1245,
                value: 0,
                isCustom: false,
            },
            {
                time: 1260,
                value: 0,
                isCustom: false,
            },
            {
                time: 1275,
                value: 8,
                isCustom: true,
            },
            {
                time: 1290,
                value: 8,
                isCustom: true,
            },
            {
                time: 1305,
                value: 10,
                isCustom: true,
            },
            {
                time: 1320,
                value: 12,
                isCustom: true,
            },
            {
                time: 1335,
                value: 14,
                isCustom: true,
            },
            {
                time: 1350,
                value: 11,
                isCustom: true,
            },
        ],
        timePeriodRatio: [1.0],
        subShifts: [
            {
                subShiftId: '18f26b07-bae4-422a-b5e4-b14a9e58e802',
                start: 1100,
                end: 1400,
                defaultPacingLimit: 0,
                turnTimes: [
                    {
                        configurationTargetId: null,
                        partySize: 1,
                        value: 120,
                    },
                    {
                        configurationTargetId: null,
                        partySize: 2,
                        value: 120,
                    },
                    {
                        configurationTargetId: null,
                        partySize: 3,
                        value: 120,
                    },
                    {
                        configurationTargetId: null,
                        partySize: 4,
                        value: 120,
                    },
                    {
                        configurationTargetId: null,
                        partySize: 5,
                        value: 150,
                    },
                    {
                        configurationTargetId: null,
                        partySize: 6,
                        value: 150,
                    },
                    {
                        configurationTargetId: null,
                        partySize: 7,
                        value: 150,
                    },
                    {
                        configurationTargetId: null,
                        partySize: 8,
                        value: 150,
                    },
                    {
                        configurationTargetId: null,
                        partySize: 9,
                        value: 150,
                    },
                    {
                        configurationTargetId: null,
                        partySize: 10,
                        value: 150,
                    },
                    {
                        configurationTargetId: null,
                        partySize: 11,
                        value: 150,
                    },
                    {
                        configurationTargetId: null,
                        partySize: 12,
                        value: 150,
                    },
                ],
                partySizeAvailability: {
                    largePartiesReservable: true,
                },
                tableAvailability: {
                    largeCombinations: {
                        reservable: true,
                        bookingChannels: {
                            online: true,
                            inHouse: true,
                            walkIn: true,
                        },
                        matchedIds: ['69e75b85', 'c040878f', '6900bfd6', 'e37a190a', '4aa64587', '6adc5600', '9621c868'],
                    },
                    largeStandaloneTables: null,
                    standardRules: [
                        {
                            reservable: true,
                            bookingChannels: {
                                online: true,
                                inHouse: true,
                                walkIn: true,
                            },
                            category: 'standard',
                            floorPlanId: null,
                            matchedTables: [
                                'cd08f0aa',
                                '03471d2b',
                                '1c1f3ba9',
                                '4ca4bac3',
                                'b45560d4',
                                '48f0c938',
                                'a76cb860',
                                '5acb74cf',
                                '545f06f0',
                                '6f774d22',
                                '00572c0a',
                                'ac9db0d9',
                                'a1b8dca8',
                                '8a25ee59',
                                '800bb17d',
                            ],
                            matchedCombinations: [
                                'bc7a03c9',
                                'c99420d3',
                                '1b51edd2',
                                'ec70bf4b',
                                'a86b332b',
                                '5e9b78a0',
                                'e449bb4d',
                                'a1ee206e',
                                'd253b48d',
                                '20aca2c8',
                                'cb3047de',
                                '342acafb',
                                '0f2b143f',
                                'f43d95f3',
                                'ba140b13',
                            ],
                        },
                        {
                            reservable: true,
                            bookingChannels: {
                                online: true,
                                inHouse: true,
                                walkIn: true,
                            },
                            category: 'highTop',
                            floorPlanId: null,
                            matchedTables: ['25135c3b', '2b8794b2'],
                            matchedCombinations: ['a210edf4'],
                        },
                        {
                            reservable: true,
                            bookingChannels: {
                                online: true,
                                inHouse: true,
                                walkIn: true,
                            },
                            category: 'counter',
                            floorPlanId: null,
                            matchedTables: ['438d8c16', '7d804b38', '08ac3349', '1cca2987', 'ca6cd853', 'ce3fe7fd'],
                            matchedCombinations: ['195eff36', '816b8b0b', 'c548f139', '3f4e56ba'],
                        },
                    ],
                    advancedRules: [
                        {
                            reservable: true,
                            bookingChannels: {
                                online: false,
                                inHouse: true,
                                walkIn: true,
                            },
                            category: null,
                            floorPlanId: null,
                            tableIds: ['16882893', '18ceeea5'],
                            tableCombinations: [],
                        },
                        {
                            reservable: true,
                            bookingChannels: {
                                online: false,
                                inHouse: true,
                                walkIn: true,
                            },
                            category: null,
                            floorPlanId: null,
                            tableIds: ['d6926415', 'b2eb4264'],
                            tableCombinations: [],
                        },
                        {
                            reservable: false,
                            bookingChannels: {
                                online: false,
                                inHouse: false,
                                walkIn: true,
                            },
                            category: null,
                            floorPlanId: null,
                            tableIds: [
                                '9318083d',
                                '43392e12',
                                '8c94b0b2',
                                '37579ca9',
                                'cc16306a',
                                '494f0a3c',
                                '55145f23',
                                '0dde7413',
                                '4dc00863',
                                'e6639082',
                            ],
                            tableCombinations: ['c01e518d', '6a5552c0', '5320911f', '36190e9a', '4f614bd4'],
                        },
                        {
                            reservable: false,
                            bookingChannels: {
                                online: false,
                                inHouse: false,
                                walkIn: true,
                            },
                            category: null,
                            floorPlanId: null,
                            tableIds: ['7ec585ba', 'cdef1c21'],
                            tableCombinations: [],
                        },
                    ],
                },
                partySizePacingLimits: [],
                targetedPacingLimits: null,
            },
        ],
        color: 1,
        onlineWaitlist: {
            enabled: true,
            timePeriods: [
                {
                    enabled: true,
                    start: 1140,
                    end: 1350,
                },
            ],
        },
        maxCapacity: 0,
        aLaCarteUnavailable: false,
        coverRestrictions: null,
        partySizeRestrictions: null,
        hasBeenOptimized: false,
        largePartyInTakeInterval: null,
        largePartyMaxConcurrent: null,
        id: '67d17d7f353b7d70e616d51c',
        timestamp: 1746783910982,
        seatingPlans: [
            {
                floorPlanId: '67c861ad3a6a4e9b4811bd73',
                maxCapacity: 0,
            },
            {
                floorPlanId: '6813a480e054a8632fcdefb2',
                maxCapacity: 0,
            },
        ],
        configurationTargets: null,
    });
