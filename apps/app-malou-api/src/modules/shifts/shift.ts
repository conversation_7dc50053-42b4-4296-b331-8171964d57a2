import { padStart } from 'lodash';

import { ShiftDto } from '@malou-io/package-dto';
import { Day, dayOrder, RemoveMethodsFromClass } from '@malou-io/package-utils';

import { OpenTableShiftView } from ':providers/opentable/interfaces/opentable.schedules.interface';
import { OpenTableShift } from ':providers/opentable/interfaces/opentable.shifts.interface';

type ShiftProps = RemoveMethodsFromClass<Shift>;

export class Shift {
    name: string;
    days: Day[];
    startTime: string;
    endTime: string;

    constructor(props: ShiftProps) {
        this.name = props.name;
        this.days = props.days;
        this.startTime = props.startTime;
        this.endTime = props.endTime;
    }

    static fromOpenTable(shift: OpenTableShift | OpenTableShiftView): Shift {
        const shiftDays = (shift as OpenTableShift)?.shiftDays ?? (shift as OpenTableShiftView)?.recurringShiftDays;
        // Change 1140 in 11:40
        const startTime = this.fromOpenTableTime(shift.firstSeating);
        const endTime = this.fromOpenTableTime(shift.lastSeating);
        const days = shiftDays
            ? Object.entries(shiftDays)
                  .filter(([_, isOpen]) => isOpen)
                  .map(([day, _]) => day.toUpperCase() as Day)
                  .sort((a, b) => dayOrder[a] - dayOrder[b])
            : [];
        return new Shift({
            name: shift.name,
            days,
            startTime,
            endTime,
        });
    }

    static fromOpenTableTime(time: number): string {
        const hours = Math.floor(time / 60);
        const minutes = time % 60;
        return `${padStart(hours.toString(), 2, '0')}:${padStart(minutes.toString(), 2, '0')}`;
    }

    toDto(): ShiftDto {
        return {
            name: this.name,
            days: this.days,
            startTime: this.startTime,
            endTime: this.endTime,
        };
    }
}
