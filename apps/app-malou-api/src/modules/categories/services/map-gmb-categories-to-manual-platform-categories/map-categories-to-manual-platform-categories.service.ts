import lodash from 'lodash';
import { singleton } from 'tsyringe';

import { ICategory } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { gmbCategoriesToOpenTableCategories } from ':modules/categories/services/map-gmb-categories-to-manual-platform-categories/mappings/gmb-to-opentable.mapping';
import { gmbCategoriesToResyCategories } from ':modules/categories/services/map-gmb-categories-to-manual-platform-categories/mappings/gmb-to-resy.mapping';
import { gmbCategoriesToTripadvisorCategories } from ':modules/categories/services/map-gmb-categories-to-manual-platform-categories/mappings/gmb-to-tripadvisor.mapping';
import { GmbCategoryIdEnum } from ':modules/categories/types';

@singleton()
export class MapCategoriesToPlatformCategoriesService {
    execute({
        categories,
        platformKey,
        categoriesInDb,
    }: {
        categories: string[];
        platformKey: PlatformKey;
        categoriesInDb: Pick<ICategory, '_id' | 'categoryName' | 'categoryId'>[];
    }): string[] {
        const platformMapping: Partial<Record<GmbCategoryIdEnum, string>> = {
            [PlatformKey.TRIPADVISOR]: gmbCategoriesToTripadvisorCategories,
            [PlatformKey.RESY]: gmbCategoriesToResyCategories,
            [PlatformKey.OPENTABLE]: gmbCategoriesToOpenTableCategories,
        }[platformKey];

        const mappedCategories = categories
            .map((categoryName) => {
                // Map categories to GMB category IDs (because only translation is stored in DB right now)
                // TODO store categoryId instead of a text to avoid this step
                const categoryId = categoriesInDb?.find((catInDB) =>
                    Object.values(catInDB.categoryName).includes(categoryName)
                )?.categoryId;
                if (!categoryId) {
                    logger.warn(`[Categories Mapping] Category ID not found in the database`, {
                        categoryName,
                    });
                    return undefined;
                }

                const categoryNameInDb = categoriesInDb?.find((catInDB) => catInDB.categoryId === categoryId)?.categoryName?.fr;
                return platformMapping?.[categoryId] || categoryNameInDb;
            })
            .filter((categoryName) => !!categoryName);

        return lodash.uniq(mappedCategories);
    }
}
