import { singleton } from 'tsyringe';

import OpenTableCredentialsUseCases from ':modules/credentials/platforms/opentable/opentable.use-cases';
import { MalouRestaurantSearchResult } from ':modules/platforms/use-cases/search-social-ids/search-social-ids.interface';
import { OpenTableLightRestaurant } from ':providers/opentable/interfaces/opentable.restaurant.interface';

@singleton()
export class SearchOpenTableSocialIdsUseCase {
    constructor(private readonly _openTableCredentialsUseCases: OpenTableCredentialsUseCases) {}

    async execute({ socialId }: { socialId: string }): Promise<{ list: MalouRestaurantSearchResult[] }> {
        const restaurant = await this._openTableCredentialsUseCases.getRestaurant({ socialId });
        return {
            list: [this._mapRestaurantToMalou(restaurant)],
        };
    }

    private _mapRestaurantToMalou(restaurant: OpenTableLightRestaurant): MalouRestaurantSearchResult {
        return {
            socialId: restaurant.rid?.toString(),
            name: restaurant.name,
            address: {
                regionCode: restaurant.address?.provinceCode ?? undefined,
                locality: restaurant.address?.city,
                postalCode: restaurant.address?.postalCode ?? undefined,
                formattedAddress:
                    (restaurant.address?.street1 ?? '') + ', ' + (restaurant.address?.street2 ?? '') + ', ' + restaurant.address?.city,
                country: restaurant.address?.countryCode,
            },
        };
    }
}
