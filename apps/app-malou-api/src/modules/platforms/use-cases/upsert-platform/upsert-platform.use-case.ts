import { singleton } from 'tsyringe';

import { IPlatformWithCredentials, toDbId } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { DuplicateSegmentAnalysisParentTopicsForRestaurantService } from ':modules/segment-analysis-parent-topics/services/duplicate-segment-analysis-parent-topics-for-restaurant/duplicate-segment-analysis-parent-topics-for-restaurant.service';

@singleton()
export class UpsertPlatformUseCase {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _duplicateSegmentAnalysisParentTopicsService: DuplicateSegmentAnalysisParentTopicsForRestaurantService
    ) {}

    async execute(restaurantId: string, platformKey: PlatformKey, mappedData): Promise<IPlatformWithCredentials<PlatformKey>> {
        const platform = (await this._platformsRepository.upsert({
            filter: { restaurantId: toDbId(restaurantId), key: platformKey },
            update: mappedData,
            options: { lean: true, populate: [{ path: 'credentials' }] },
        })) as IPlatformWithCredentials<PlatformKey>;

        if (platform && platform.socialId) {
            try {
                const existingPlatforms = await this._platformsRepository.getPlatformsBySocialIdAndPlatformKey(
                    platform.socialId,
                    platformKey
                );
                if (existingPlatforms.length > 1) {
                    // If platform already existed in db, it means it has reviews with semantic analysis
                    // Parent topics linked to these reviews now need to also be linked to new parent topic associated to this restaurant
                    await this._duplicateSegmentAnalysisParentTopicsService.execute({
                        platformSocialId: platform.socialId,
                        platformKey: platform.key,
                        restaurantId: restaurantId.toString(),
                    });
                }
            } catch (err) {
                logger.error('[UpsertPlatformUseCase] Error while duplicating segment analysis parent topics', err);
            }
        }

        return platform;
    }
}
