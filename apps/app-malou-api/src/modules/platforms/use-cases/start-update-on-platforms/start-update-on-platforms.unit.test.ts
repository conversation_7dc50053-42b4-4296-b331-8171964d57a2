import { randomUUID } from 'node:crypto';
import { container } from 'tsyringe';

import { DbId, IInformationUpdate } from '@malou-io/package-models';
import {
    InformationUpdatePlatformStateStatus,
    InformationUpdateProvider,
    InformationUpdateStatus,
    PlatformAccessStatus,
    PlatformAccessType,
    PlatformKey,
    YextPublisherId,
} from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultInformationUpdate } from ':modules/information-updates/tests/information-updates.builder';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { PublishOnConnectedPlatformsUseCase } from ':modules/platforms/use-cases/publish-on-connected-platforms/publish-on-connected-platforms.use-case';
import { StartUpdateOnPlatformsUseCase } from ':modules/platforms/use-cases/start-update-on-platforms/start-update-on-platforms.use-case';
import { getDefaultYextAccount } from ':modules/publishers/yext/tests/yext-account.builder';
import { getDefaultYextLocation } from ':modules/publishers/yext/tests/yext-location.builder';
import UpdateLocationUseCase from ':modules/publishers/yext/use-cases/update-location/update-location.use-case';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { OpenTableProvider } from ':providers/opentable/opentable.provider';
import { YextProvider } from ':providers/yext/yext.provider';
import { YextListingStatus } from ':providers/yext/yext.provider.interfaces';
import { YextProviderMock } from ':providers/yext/yext.provider.mock';
import * as experimentationService from ':services/experimentations-service/experimentation.service';
import { SlackService } from ':services/slack.service';

let publishOnConnectedPlatformsUseCase: PublishOnConnectedPlatformsUseCase;
let updateLocationUseCase: UpdateLocationUseCase;

describe('StartUpdateOnPlatformsUseCase', () => {
    beforeEach(() => {
        container.reset();
        registerRepositories([
            'RestaurantsRepository',
            'PlatformsRepository',
            'YextLocationRepository',
            'YextAccountRepository',
            'InformationUpdatesRepository',
        ]);

        publishOnConnectedPlatformsUseCase = {} as PublishOnConnectedPlatformsUseCase;
        container.registerInstance(PublishOnConnectedPlatformsUseCase, publishOnConnectedPlatformsUseCase);
        updateLocationUseCase = {} as UpdateLocationUseCase;
        container.registerInstance(UpdateLocationUseCase, updateLocationUseCase);

        const slackServiceMock = {
            sendMessage: jest.fn(),
            createContextForSlack: jest.fn(),
            sendAlert: jest.fn(),
        } as unknown as SlackService;
        container.register(SlackService, { useValue: slackServiceMock });

        jest.spyOn(experimentationService, 'isFeatureAvailableForRestaurant').mockResolvedValue(false);

        const OpenTableProviderStub = {
            optIn: jest.fn(),
            optOut: jest.fn(),
        } as unknown as OpenTableProvider;
        container.registerInstance(OpenTableProvider, OpenTableProviderStub);
    });

    describe('execute', () => {
        it('should always update Gmb platform', async () => {
            container.register(YextProvider, { useValue: new YextProviderMock() });
            const startUpdateOnPlatformsUseCase = container.resolve(StartUpdateOnPlatformsUseCase);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms' | 'informationUpdates'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant()
                                    .access([
                                        {
                                            platformKey: PlatformKey.GMB,
                                            status: PlatformAccessStatus.VERIFIED,
                                            active: true,
                                            accessType: PlatformAccessType.AUTO,
                                            lastUpdated: new Date(),
                                        },
                                    ])
                                    .build(),
                            ];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.GMB).build()];
                        },
                    },
                    informationUpdates: {
                        data(dependencies) {
                            return [
                                getDefaultInformationUpdate()
                                    .data({
                                        name: 'New restaurant name',
                                        categoryName: 'category',
                                    })
                                    .platformStates([
                                        {
                                            key: 'gmb',
                                            status: InformationUpdatePlatformStateStatus.DONE,
                                            provider: InformationUpdateProvider.MALOU,
                                        },
                                        {
                                            key: 'facebook',
                                            status: InformationUpdatePlatformStateStatus.DONE,
                                            provider: InformationUpdateProvider.MALOU,
                                        },
                                        {
                                            key: 'tripadvisor',
                                            status: InformationUpdatePlatformStateStatus.DONE,
                                            provider: InformationUpdateProvider.MALOU,
                                        },
                                    ])
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(_) {
                    return {};
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            publishOnConnectedPlatformsUseCase.execute = jest.fn().mockReturnValue({ success: true });
            const spy = jest.spyOn(publishOnConnectedPlatformsUseCase, 'execute');
            const restaurantId = seededObjects.restaurants[0]._id as DbId;
            await startUpdateOnPlatformsUseCase.execute(seededObjects.informationUpdates[0] as IInformationUpdate);

            expect(spy).toHaveBeenCalledWith(
                expect.objectContaining({
                    platformKey: PlatformKey.GMB,
                    restaurant: expect.objectContaining({
                        _id: restaurantId,
                    }),
                })
            );
        });

        it('should update via Yext if the restaurant can use Yext', async () => {
            container.register(YextProvider, { useValue: new YextProviderMock() });
            const startUpdateOnPlatformsUseCase = container.resolve(StartUpdateOnPlatformsUseCase);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms' | 'yextAccounts' | 'yextLocations' | 'informationUpdates'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant()
                                    .access([
                                        {
                                            platformKey: PlatformKey.GMB,
                                            status: PlatformAccessStatus.VERIFIED,
                                            active: true,
                                            accessType: PlatformAccessType.MANAGER,
                                            lastUpdated: new Date(),
                                        },
                                    ])
                                    .isYextActivated(true)
                                    .build(),
                            ];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.GMB).build()];
                        },
                    },
                    yextAccounts: {
                        data(dependencies) {
                            return [getDefaultYextAccount().organizationId(dependencies.restaurants()[0].organizationId!).build()];
                        },
                    },
                    yextLocations: {
                        data(dependencies) {
                            return [
                                getDefaultYextLocation()
                                    .yextAccountId(dependencies.yextAccounts()[0]._id)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    informationUpdates: {
                        data(dependencies) {
                            return [
                                getDefaultInformationUpdate()
                                    .data({
                                        name: 'New restaurant name',
                                        categoryName: 'category',
                                    })
                                    .platformStates([
                                        {
                                            key: 'gmb',
                                            status: InformationUpdatePlatformStateStatus.DONE,
                                            provider: InformationUpdateProvider.MALOU,
                                        },
                                        {
                                            key: 'facebook',
                                            status: InformationUpdatePlatformStateStatus.DONE,
                                            provider: InformationUpdateProvider.MALOU,
                                        },
                                        {
                                            key: 'tripadvisor',
                                            status: InformationUpdatePlatformStateStatus.DONE,
                                            provider: InformationUpdateProvider.MALOU,
                                        },
                                    ])
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(_) {
                    return {};
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            publishOnConnectedPlatformsUseCase.execute = jest.fn().mockReturnValue({ success: true });
            updateLocationUseCase.execute = jest.fn().mockReturnValue({});

            const updateLocationSpy = jest.spyOn(updateLocationUseCase, 'execute');
            const informationUpdate = seededObjects.informationUpdates[0] as IInformationUpdate;
            await startUpdateOnPlatformsUseCase.execute(informationUpdate);

            expect(updateLocationSpy).toHaveBeenCalled();
        });

        it('should update auto platforms not updated by Yext via Malou update', async () => {
            container.register(YextProvider, { useValue: new YextProviderMock() });
            const startUpdateOnPlatformsUseCase = container.resolve(StartUpdateOnPlatformsUseCase);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms' | 'yextAccounts' | 'yextLocations' | 'informationUpdates'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant()
                                    .access([
                                        {
                                            platformKey: PlatformKey.GMB,
                                            status: PlatformAccessStatus.VERIFIED,
                                            active: true,
                                            accessType: PlatformAccessType.AUTO,
                                            lastUpdated: new Date(),
                                        },
                                        {
                                            platformKey: PlatformKey.FACEBOOK,
                                            status: PlatformAccessStatus.VERIFIED,
                                            active: true,
                                            accessType: PlatformAccessType.AUTO,
                                            lastUpdated: new Date(),
                                        },
                                    ])
                                    .isYextActivated(true)
                                    .build(),
                            ];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.GMB).build(),
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.FACEBOOK).build(),
                            ];
                        },
                    },
                    yextAccounts: {
                        data(dependencies) {
                            return [getDefaultYextAccount().organizationId(dependencies.restaurants()[0].organizationId!).build()];
                        },
                    },
                    yextLocations: {
                        data(dependencies) {
                            return [
                                getDefaultYextLocation()
                                    .yextAccountId(dependencies.yextAccounts()[0]._id)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    informationUpdates: {
                        data(dependencies) {
                            return [
                                getDefaultInformationUpdate()
                                    .data({
                                        name: 'New restaurant name',
                                        categoryName: 'category',
                                    })
                                    .platformStates([
                                        {
                                            key: 'gmb',
                                            status: InformationUpdatePlatformStateStatus.DONE,
                                            provider: InformationUpdateProvider.MALOU,
                                        },
                                        {
                                            key: 'facebook',
                                            status: InformationUpdatePlatformStateStatus.DONE,
                                            provider: InformationUpdateProvider.MALOU,
                                        },
                                        {
                                            key: 'tripadvisor',
                                            status: InformationUpdatePlatformStateStatus.DONE,
                                            provider: InformationUpdateProvider.MALOU,
                                        },
                                    ])
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(_) {
                    return {};
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            publishOnConnectedPlatformsUseCase.execute = jest.fn().mockReturnValue({ success: true });
            updateLocationUseCase.execute = jest.fn().mockReturnValue({});

            const spy = jest.spyOn(publishOnConnectedPlatformsUseCase, 'execute');
            const restaurantId = seededObjects.restaurants[0]._id as DbId;
            const informationUpdate = seededObjects.informationUpdates[0] as IInformationUpdate;
            await startUpdateOnPlatformsUseCase.execute(informationUpdate);

            expect(spy).toHaveBeenCalledWith(
                expect.objectContaining({
                    platformKey: PlatformKey.FACEBOOK,
                    restaurant: expect.objectContaining({
                        _id: restaurantId,
                    }),
                })
            );
        });

        it('should not update platforms via Malou for platforms updated by Yext', async () => {
            const yextProviderMock = new YextProviderMock();
            yextProviderMock.getListingsForLocation = jest.fn().mockReturnValue({
                meta: {
                    uuid: randomUUID(),
                },
                response: {
                    count: 0,
                    listings: [
                        {
                            publisherId: YextPublisherId.TRIPADVISOR,
                        },
                    ],
                    pageToken: '',
                },
            });
            container.register(YextProvider, { useValue: yextProviderMock });
            const startUpdateOnPlatformsUseCase = container.resolve(StartUpdateOnPlatformsUseCase);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms' | 'yextAccounts' | 'yextLocations' | 'informationUpdates'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant()
                                    .access([
                                        {
                                            platformKey: PlatformKey.GMB,
                                            status: PlatformAccessStatus.VERIFIED,
                                            active: true,
                                            accessType: PlatformAccessType.AUTO,
                                            lastUpdated: new Date(),
                                        },
                                        {
                                            platformKey: PlatformKey.TRIPADVISOR,
                                            status: PlatformAccessStatus.VERIFIED,
                                            active: true,
                                            accessType: PlatformAccessType.MANAGER,
                                            lastUpdated: new Date(),
                                        },
                                    ])
                                    .isYextActivated(true)
                                    .build(),
                            ];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.GMB).build(),
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.TRIPADVISOR).build(),
                            ];
                        },
                    },
                    yextAccounts: {
                        data(dependencies) {
                            return [getDefaultYextAccount().organizationId(dependencies.restaurants()[0].organizationId!).build()];
                        },
                    },
                    yextLocations: {
                        data(dependencies) {
                            return [
                                getDefaultYextLocation()
                                    .yextAccountId(dependencies.yextAccounts()[0]._id)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    informationUpdates: {
                        data(dependencies) {
                            return [
                                getDefaultInformationUpdate()
                                    .data({
                                        name: 'New restaurant name',
                                        categoryName: 'category',
                                    })
                                    .platformStates([
                                        {
                                            key: 'gmb',
                                            status: InformationUpdatePlatformStateStatus.DONE,
                                            provider: InformationUpdateProvider.MALOU,
                                        },
                                        {
                                            key: 'facebook',
                                            status: InformationUpdatePlatformStateStatus.DONE,
                                            provider: InformationUpdateProvider.MALOU,
                                        },
                                        {
                                            key: 'tripadvisor',
                                            status: InformationUpdatePlatformStateStatus.DONE,
                                            provider: InformationUpdateProvider.MALOU,
                                        },
                                    ])
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(_) {
                    return {};
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            publishOnConnectedPlatformsUseCase.execute = jest.fn().mockReturnValue({ success: true });
            updateLocationUseCase.execute = jest.fn().mockReturnValue({});

            const spy = jest.spyOn(publishOnConnectedPlatformsUseCase, 'execute');
            const restaurantId = seededObjects.restaurants[0]._id as DbId;
            const informationUpdate = seededObjects.informationUpdates[0] as IInformationUpdate;
            await startUpdateOnPlatformsUseCase.execute(informationUpdate);

            expect(spy).not.toHaveBeenCalledWith(
                expect.objectContaining({
                    platformKey: PlatformKey.TRIPADVISOR,
                    restaurant: expect.objectContaining({
                        _id: restaurantId,
                    }),
                })
            );
        });

        it('should update auto platforms via Malou for platforms with Yext update that leads to an error', async () => {
            const yextProviderMock = new YextProviderMock();
            yextProviderMock.getListingsForLocation = jest.fn().mockReturnValue({
                meta: {
                    uuid: randomUUID(),
                },
                response: {
                    count: 0,
                    listings: [
                        {
                            publisherId: YextPublisherId.TRIPADVISOR,
                            status: YextListingStatus.UNAVAILABLE,
                        },
                    ],
                    pageToken: '',
                },
            });
            container.register(YextProvider, { useValue: yextProviderMock });
            const startUpdateOnPlatformsUseCase = container.resolve(StartUpdateOnPlatformsUseCase);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms' | 'yextAccounts' | 'yextLocations' | 'informationUpdates'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant()
                                    .access([
                                        {
                                            platformKey: PlatformKey.GMB,
                                            status: PlatformAccessStatus.VERIFIED,
                                            active: true,
                                            accessType: PlatformAccessType.AUTO,
                                            lastUpdated: new Date(),
                                        },
                                        {
                                            platformKey: PlatformKey.FACEBOOK,
                                            status: PlatformAccessStatus.VERIFIED,
                                            active: true,
                                            accessType: PlatformAccessType.AUTO,
                                            lastUpdated: new Date(),
                                        },
                                    ])
                                    .isYextActivated(true)
                                    .build(),
                            ];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.GMB).build(),
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.FACEBOOK).build(),
                            ];
                        },
                    },
                    yextAccounts: {
                        data(dependencies) {
                            return [getDefaultYextAccount().organizationId(dependencies.restaurants()[0].organizationId!).build()];
                        },
                    },
                    yextLocations: {
                        data(dependencies) {
                            return [
                                getDefaultYextLocation()
                                    .yextAccountId(dependencies.yextAccounts()[0]._id)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    informationUpdates: {
                        data(dependencies) {
                            return [
                                getDefaultInformationUpdate()
                                    .data({
                                        name: 'New restaurant name',
                                        categoryName: 'category',
                                    })
                                    .platformStates([
                                        {
                                            key: 'gmb',
                                            status: InformationUpdatePlatformStateStatus.DONE,
                                            provider: InformationUpdateProvider.MALOU,
                                        },
                                        {
                                            key: 'facebook',
                                            status: InformationUpdatePlatformStateStatus.DONE,
                                            provider: InformationUpdateProvider.MALOU,
                                        },
                                        {
                                            key: 'tripadvisor',
                                            status: InformationUpdatePlatformStateStatus.DONE,
                                            provider: InformationUpdateProvider.MALOU,
                                        },
                                    ])
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(_) {
                    return {};
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            publishOnConnectedPlatformsUseCase.execute = jest.fn().mockReturnValue({ success: true });
            updateLocationUseCase.execute = jest.fn().mockReturnValue({});

            const spy = jest.spyOn(publishOnConnectedPlatformsUseCase, 'execute');
            const restaurantId = seededObjects.restaurants[0]._id as DbId;
            const informationUpdate = seededObjects.informationUpdates[0] as IInformationUpdate;
            await startUpdateOnPlatformsUseCase.execute(informationUpdate);

            expect(spy).toHaveBeenCalledWith(
                expect.objectContaining({
                    platformKey: PlatformKey.FACEBOOK,
                    restaurant: expect.objectContaining({
                        _id: restaurantId,
                    }),
                })
            );
        });

        it('should validate pending information updates for platforms updated by Yext', async () => {
            const yextProviderMock = new YextProviderMock();
            yextProviderMock.getListingsForLocation = jest.fn().mockReturnValue({
                meta: {
                    uuid: randomUUID(),
                },
                response: {
                    count: 0,
                    listings: [
                        {
                            publisherId: YextPublisherId.TRIPADVISOR,
                            status: YextListingStatus.LIVE,
                        },
                    ],
                    pageToken: '',
                },
            });
            container.register(YextProvider, { useValue: yextProviderMock });
            const startUpdateOnPlatformsUseCase = container.resolve(StartUpdateOnPlatformsUseCase);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms' | 'yextAccounts' | 'yextLocations' | 'informationUpdates'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant()
                                    .access([
                                        {
                                            platformKey: PlatformKey.GMB,
                                            status: PlatformAccessStatus.VERIFIED,
                                            active: true,
                                            accessType: PlatformAccessType.AUTO,
                                            lastUpdated: new Date(),
                                        },
                                        {
                                            platformKey: PlatformKey.TRIPADVISOR,
                                            status: PlatformAccessStatus.VERIFIED,
                                            active: true,
                                            accessType: PlatformAccessType.MANAGER,
                                            lastUpdated: new Date(),
                                        },
                                    ])
                                    .isYextActivated(true)
                                    .build(),
                            ];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.GMB).build(),
                                getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.TRIPADVISOR).build(),
                            ];
                        },
                    },
                    yextAccounts: {
                        data(dependencies) {
                            return [getDefaultYextAccount().organizationId(dependencies.restaurants()[0].organizationId!).build()];
                        },
                    },
                    yextLocations: {
                        data(dependencies) {
                            return [
                                getDefaultYextLocation()
                                    .yextAccountId(dependencies.yextAccounts()[0]._id)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    informationUpdates: {
                        data(dependencies) {
                            return [
                                getDefaultInformationUpdate()
                                    .platformStates([
                                        {
                                            key: PlatformKey.TRIPADVISOR,
                                            status: InformationUpdatePlatformStateStatus.PENDING,
                                            updateDoneAt: new Date(),
                                            provider: InformationUpdateProvider.YEXT,
                                        },
                                    ])
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .status(InformationUpdateStatus.VALIDATED)
                                    .data({
                                        name: 'New restaurant name',
                                    })
                                    .previousData({})
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return {
                        key: PlatformKey.TRIPADVISOR,
                        status: InformationUpdatePlatformStateStatus.DONE,
                        updateDoneAt: expect.any(Date),
                        provider: InformationUpdateProvider.YEXT,
                        yextStatus: YextListingStatus.LIVE,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            publishOnConnectedPlatformsUseCase.execute = jest.fn().mockReturnValue({ success: true });
            updateLocationUseCase.execute = jest.fn().mockReturnValue({});

            const expectedResult = testCase.getExpectedResult();
            const seededInformationUpdate = seededObjects.informationUpdates[0] as IInformationUpdate;
            const result = await startUpdateOnPlatformsUseCase.execute(seededInformationUpdate);

            const platformStateUpdated = result.platformStates.find((platformState) => platformState.key === PlatformKey.TRIPADVISOR);
            expect(platformStateUpdated).toEqual(expectedResult);
        });
    });
});
