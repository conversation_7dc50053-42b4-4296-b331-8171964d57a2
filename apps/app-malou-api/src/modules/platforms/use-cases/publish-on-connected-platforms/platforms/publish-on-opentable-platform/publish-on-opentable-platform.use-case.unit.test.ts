import { container } from 'tsyringe';

import { newDbId, toDbId } from '@malou-io/package-models';
import { ApplicationLanguage, Day, DescriptionSize, MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { DateProviderPort } from ':helpers/providers/date/date.provider';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultCategory } from ':modules/categories/tests/categories.builder';
import { GmbCategoryIdEnum } from ':modules/categories/types';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { PublishOnOpenTablePlatformUseCase } from ':modules/platforms/use-cases/publish-on-connected-platforms/platforms/publish-on-opentable-platform/publish-on-opentable-platform.use-case';
import { RestaurantPopulatedToPublish } from ':modules/platforms/use-cases/publish-on-connected-platforms/publish-on-connected-platforms.use-case';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultShift } from ':modules/shifts/tests/shift.builder';
import { OpenTableProvider } from ':providers/opentable/opentable.provider';

let publishOnOpenTablePlatformUseCase: PublishOnOpenTablePlatformUseCase;

describe('PublishOnOpenTablePlatformUseCase', () => {
    let dateProvider: DateProviderPort;

    beforeAll(() => {
        const openTableProviderStub = {
            patchRestaurant: jest.fn().mockResolvedValue(undefined),
        } as unknown as jest.Mocked<OpenTableProvider>;
        init({ openTableProviderStub });
    });

    describe('execute', () => {
        it('should throw an error if there is no OpenTable platform', async () => {
            const restaurantsRepository = container.resolve(RestaurantsRepository);
            const restaurantId = newDbId();
            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).build()];
                        },
                    },
                    platforms: {
                        data() {
                            return [getDefaultPlatform().name('platform_0').restaurantId(restaurantId).key(PlatformKey.GMB).build()];
                        },
                    },
                },
                expectedErrorCode: MalouErrorCode.PLATFORM_NOT_FOUND,
            });
            await testCase.build();
            const restaurant: RestaurantPopulatedToPublish = (await restaurantsRepository.findOne({
                filter: { _id: toDbId(restaurantId) },
                options: {
                    lean: true,
                    populate: [
                        { path: 'category' },
                        { path: 'logo' },
                        { path: 'cover' },
                        { path: 'categoryList' },
                        { path: 'attributeList', populate: [{ path: 'attribute' }] },
                    ],
                },
            })) as RestaurantPopulatedToPublish;
            const keysToUpdate: (keyof RestaurantPopulatedToPublish)[] = ['name'];

            const expectedErrorCode = testCase.getExpectedErrorCode();
            await expect(publishOnOpenTablePlatformUseCase.execute({ restaurant, keysToUpdate })).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: expectedErrorCode,
                })
            );
        });

        it('should update all fields except specialHours', async () => {
            const restaurantsRepository = container.resolve(RestaurantsRepository);
            const categoryId = newDbId();
            const restaurantId = newDbId();
            const testCase = new TestCaseBuilderV2<'categories' | 'restaurants' | 'platforms'>({
                seeds: {
                    categories: {
                        data() {
                            return [getDefaultCategory()._id(categoryId).categoryId(GmbCategoryIdEnum.AFRICAN_RESTAURANT).build()];
                        },
                    },
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant()
                                    ._id(restaurantId)
                                    .phone({ prefix: 33, digits: 607080910 })
                                    .website('https://www.malou.fr')
                                    .descriptions([
                                        {
                                            _id: newDbId(),
                                            language: ApplicationLanguage.FR,
                                            text: 'Description FR',
                                            size: DescriptionSize.LONG,
                                            createdAt: new Date(),
                                            updatedAt: new Date(),
                                        },
                                    ])
                                    .menuUrl('https://www.malou.fr/menu')
                                    .regularHours([
                                        {
                                            isClosed: false,
                                            closeDay: Day.MONDAY,
                                            closeTime: '13:35',
                                            openDay: Day.MONDAY,
                                            openTime: '11:40',
                                        },
                                    ])
                                    .category(categoryId)
                                    .build(),
                            ];
                        },
                    },
                    platforms: {
                        data() {
                            return [getDefaultPlatform().socialId('123').restaurantId(restaurantId).key(PlatformKey.OPENTABLE).build()];
                        },
                    },
                },
                expectedResult() {
                    return {
                        success: true,
                        errors: [],
                    };
                },
            });
            await testCase.build();
            const restaurant: RestaurantPopulatedToPublish = (await restaurantsRepository.findOne({
                filter: { _id: toDbId(restaurantId) },
                options: {
                    lean: true,
                    populate: [
                        { path: 'category' },
                        { path: 'logo' },
                        { path: 'cover' },
                        { path: 'categoryList' },
                        { path: 'attributeList', populate: [{ path: 'attribute' }] },
                    ],
                },
            })) as RestaurantPopulatedToPublish;
            const keysToUpdate: (keyof RestaurantPopulatedToPublish)[] = [
                'phone',
                'website',
                'descriptions',
                'menuUrl',
                'regularHours',
                'category',
                'categoryList',
            ];

            const openTableProvider = container.resolve(OpenTableProvider);
            const patchRestaurantSpy = jest.spyOn(openTableProvider, 'patchRestaurant');

            const result = await publishOnOpenTablePlatformUseCase.execute({ restaurant, keysToUpdate });

            const expectedResult = testCase.getExpectedResult();
            expect(result).toEqual(expectedResult);
            expect(patchRestaurantSpy).toHaveBeenCalledTimes(1);
            expect(patchRestaurantSpy).toHaveBeenCalledWith(
                expect.objectContaining({
                    openTableRestaurant: {
                        content: { customMessages: { RestaurantDescription: { 'fr-FR': { message: 'Description FR' } } } },
                        core: {
                            foodTypes: { otherCuisines: [], primaryCuisineId: '41753431-959b-4713-8a73-994980ba8a20' },
                            messageSpecifications: [
                                {
                                    Specification: {
                                        schemaVersion: 1,
                                        specification: [
                                            {
                                                days: ['mon'],
                                                shiftEnds: '13:35',
                                                shiftStarts: '11:40',
                                                standardShiftName: { id: 'openingtimes' },
                                            },
                                        ],
                                    },
                                    Type: 'Hours',
                                },
                            ],
                            restaurant: { menuUrl: 'https://www.malou.fr/menu', phoneNumber: '+***********', url: 'https://www.malou.fr' },
                            restaurantFeatures: {
                                ProfileUpdateTimes: {
                                    BusinessHours: expect.any(String),
                                    Description: expect.any(String),
                                    Details: expect.any(String),
                                },
                            },
                        },
                    },
                    socialId: '123',
                })
            );
        });

        describe('Updating specialHours', () => {
            it('should do nothing if all special hours are in the past', async () => {
                const openTableProviderStub = {
                    patchRestaurant: jest.fn().mockResolvedValue(undefined),
                    addSpecialOpenHours: jest.fn().mockResolvedValue(undefined),
                    closeSpecialHours: jest.fn().mockResolvedValue(undefined),
                    deleteSpecialHour: jest.fn().mockResolvedValue(undefined),
                    fetchShifts: jest.fn().mockResolvedValue([getDefaultShift().firstSeating(1140).lastSeating(1335).build()]),
                    fetchSchedules: jest.fn().mockResolvedValue([]),
                } as unknown as jest.Mocked<OpenTableProvider>;
                init({ openTableProviderStub });

                const restaurantsRepository = container.resolve(RestaurantsRepository);
                const restaurantId = newDbId();
                const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [
                                    getDefaultRestaurant()
                                        ._id(restaurantId)
                                        .specialHours([
                                            {
                                                isClosed: false,
                                                startDate: { year: 2025, month: 9, day: 27 },
                                                openTime: '11:40',
                                                endDate: { year: 2025, month: 9, day: 28 },
                                                closeTime: '13:35',
                                            },
                                        ])
                                        .build(),
                                ];
                            },
                        },
                        platforms: {
                            data() {
                                return [getDefaultPlatform().socialId('123').restaurantId(restaurantId).key(PlatformKey.OPENTABLE).build()];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            success: true,
                            errors: [],
                        };
                    },
                });
                await testCase.build();
                const restaurant: RestaurantPopulatedToPublish = (await restaurantsRepository.findOne({
                    filter: { _id: toDbId(restaurantId) },
                    options: {
                        lean: true,
                        populate: [
                            { path: 'category' },
                            { path: 'logo' },
                            { path: 'cover' },
                            { path: 'categoryList' },
                            { path: 'attributeList', populate: [{ path: 'attribute' }] },
                        ],
                    },
                })) as RestaurantPopulatedToPublish;
                const keysToUpdate: (keyof RestaurantPopulatedToPublish)[] = ['specialHours'];

                const mockDateProvider = jest.fn().mockReturnValue(new Date('2026-01-01'));
                dateProvider.provideTodayDate = mockDateProvider;
                const openTableProvider = container.resolve(OpenTableProvider);
                const patchRestaurantSpy = jest.spyOn(openTableProvider, 'patchRestaurant');
                const addSpecialOpenHoursSpy = jest.spyOn(openTableProvider, 'addSpecialOpenHours');
                const closeSpecialHoursSpy = jest.spyOn(openTableProvider, 'closeSpecialHours');
                const deleteSpecialHourSpy = jest.spyOn(openTableProvider, 'deleteSpecialHour');

                const result = await publishOnOpenTablePlatformUseCase.execute({ restaurant, keysToUpdate });

                const expectedResult = testCase.getExpectedResult();
                expect(result).toEqual(expectedResult);
                expect(patchRestaurantSpy).toHaveBeenCalledTimes(0);
                expect(addSpecialOpenHoursSpy).toHaveBeenCalledTimes(0);
                expect(closeSpecialHoursSpy).toHaveBeenCalledTimes(0);
                expect(deleteSpecialHourSpy).toHaveBeenCalledTimes(0);
            });

            describe('New special hours', () => {
                it('should not add new special hours if not matching a shift', async () => {
                    const openTableProviderStub = {
                        patchRestaurant: jest.fn().mockResolvedValue(undefined),
                        addSpecialOpenHours: jest.fn().mockResolvedValue(undefined),
                        publishSpecialHoursUpdates: jest.fn().mockResolvedValue(undefined),
                        fetchShifts: jest.fn().mockResolvedValue([getDefaultShift().firstSeating(1140).lastSeating(1335).build()]),
                        fetchSchedules: jest.fn().mockResolvedValue([]),
                    } as unknown as jest.Mocked<OpenTableProvider>;
                    init({ openTableProviderStub });

                    const restaurantsRepository = container.resolve(RestaurantsRepository);
                    const restaurantId = newDbId();
                    const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms'>({
                        seeds: {
                            restaurants: {
                                data() {
                                    return [
                                        getDefaultRestaurant()
                                            ._id(restaurantId)
                                            .specialHours([
                                                {
                                                    isClosed: false,
                                                    startDate: { year: 2025, month: 9, day: 27 },
                                                    openTime: '11:40',
                                                    endDate: { year: 2025, month: 9, day: 28 },
                                                    closeTime: '14:35',
                                                },
                                            ])
                                            .build(),
                                    ];
                                },
                            },
                            platforms: {
                                data() {
                                    return [
                                        getDefaultPlatform().socialId('123').restaurantId(restaurantId).key(PlatformKey.OPENTABLE).build(),
                                    ];
                                },
                            },
                        },
                        expectedResult() {
                            return {
                                success: true,
                                errors: [],
                            };
                        },
                    });
                    await testCase.build();
                    const restaurant: RestaurantPopulatedToPublish = (await restaurantsRepository.findOne({
                        filter: { _id: toDbId(restaurantId) },
                        options: {
                            lean: true,
                            populate: [
                                { path: 'category' },
                                { path: 'logo' },
                                { path: 'cover' },
                                { path: 'categoryList' },
                                { path: 'attributeList', populate: [{ path: 'attribute' }] },
                            ],
                        },
                    })) as RestaurantPopulatedToPublish;
                    const keysToUpdate: (keyof RestaurantPopulatedToPublish)[] = ['specialHours'];

                    const mockDateProvider = jest.fn().mockReturnValue(new Date('2025-01-01'));
                    dateProvider.provideTodayDate = mockDateProvider;
                    const openTableProvider = container.resolve(OpenTableProvider);
                    const patchRestaurantSpy = jest.spyOn(openTableProvider, 'patchRestaurant');
                    const addSpecialOpenHoursSpy = jest.spyOn(openTableProvider, 'addSpecialOpenHours');
                    const publishingSpecialHoursSpy = jest.spyOn(openTableProvider, 'publishSpecialHoursUpdates');

                    const result = await publishOnOpenTablePlatformUseCase.execute({ restaurant, keysToUpdate });

                    const expectedResult = testCase.getExpectedResult();
                    expect(result).toEqual(expectedResult);
                    expect(patchRestaurantSpy).toHaveBeenCalledTimes(0);
                    expect(addSpecialOpenHoursSpy).toHaveBeenCalledTimes(0);
                    expect(publishingSpecialHoursSpy).toHaveBeenCalledTimes(0);
                });

                it('should not add new special hours if matching a shift but overlapping with another shift', async () => {
                    const openTableProviderStub = {
                        patchRestaurant: jest.fn().mockResolvedValue(undefined),
                        addSpecialOpenHours: jest.fn().mockResolvedValue(undefined),
                        publishSpecialHoursUpdates: jest.fn().mockResolvedValue(undefined),
                        fetchShifts: jest.fn().mockResolvedValue([
                            getDefaultShift()
                                .firstSeating(1100)
                                .lastSeating(1335)
                                .shiftDays({
                                    monday: true,
                                    tuesday: true,
                                    wednesday: false,
                                    thursday: false,
                                    friday: false,
                                    saturday: false,
                                    sunday: false,
                                })
                                .build(),
                            getDefaultShift()
                                .firstSeating(1140)
                                .lastSeating(1335)
                                .shiftDays({
                                    monday: false,
                                    tuesday: false,
                                    wednesday: false,
                                    thursday: true,
                                    friday: true,
                                    saturday: true,
                                    sunday: true,
                                })
                                .build(),
                        ]),
                        fetchSchedules: jest.fn().mockResolvedValue([]),
                    } as unknown as jest.Mocked<OpenTableProvider>;
                    init({ openTableProviderStub });

                    const restaurantsRepository = container.resolve(RestaurantsRepository);
                    const restaurantId = newDbId();
                    const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms'>({
                        seeds: {
                            restaurants: {
                                data() {
                                    return [
                                        getDefaultRestaurant()
                                            ._id(restaurantId)
                                            .specialHours([
                                                {
                                                    isClosed: false,
                                                    startDate: { year: 2025, month: 9, day: 27 },
                                                    openTime: '11:40',
                                                    endDate: { year: 2025, month: 9, day: 28 },
                                                    closeTime: '14:35',
                                                },
                                            ])
                                            .build(),
                                    ];
                                },
                            },
                            platforms: {
                                data() {
                                    return [
                                        getDefaultPlatform().socialId('123').restaurantId(restaurantId).key(PlatformKey.OPENTABLE).build(),
                                    ];
                                },
                            },
                        },
                        expectedResult() {
                            return {
                                success: true,
                                errors: [],
                            };
                        },
                    });
                    await testCase.build();
                    const restaurant: RestaurantPopulatedToPublish = (await restaurantsRepository.findOne({
                        filter: { _id: toDbId(restaurantId) },
                        options: {
                            lean: true,
                            populate: [
                                { path: 'category' },
                                { path: 'logo' },
                                { path: 'cover' },
                                { path: 'categoryList' },
                                { path: 'attributeList', populate: [{ path: 'attribute' }] },
                            ],
                        },
                    })) as RestaurantPopulatedToPublish;
                    const keysToUpdate: (keyof RestaurantPopulatedToPublish)[] = ['specialHours'];

                    const mockDateProvider = jest.fn().mockReturnValue(new Date('2025-01-01'));
                    dateProvider.provideTodayDate = mockDateProvider;
                    const openTableProvider = container.resolve(OpenTableProvider);
                    const patchRestaurantSpy = jest.spyOn(openTableProvider, 'patchRestaurant');
                    const addSpecialOpenHoursSpy = jest.spyOn(openTableProvider, 'addSpecialOpenHours');
                    const publishingSpecialHoursSpy = jest.spyOn(openTableProvider, 'publishSpecialHoursUpdates');

                    const result = await publishOnOpenTablePlatformUseCase.execute({ restaurant, keysToUpdate });

                    const expectedResult = testCase.getExpectedResult();
                    expect(result).toEqual(expectedResult);
                    expect(patchRestaurantSpy).toHaveBeenCalledTimes(0);
                    expect(addSpecialOpenHoursSpy).toHaveBeenCalledTimes(0);
                    expect(publishingSpecialHoursSpy).toHaveBeenCalledTimes(0);
                });

                it('should add special hours if matching a shift with a closed holiday', async () => {
                    const openTableProviderStub = {
                        patchRestaurant: jest.fn().mockResolvedValue(undefined),
                        addSpecialOpenHours: jest.fn().mockResolvedValue(undefined),
                        publishSpecialHoursUpdates: jest.fn().mockResolvedValue(undefined),
                        fetchShifts: jest
                            .fn()
                            .mockResolvedValue([getDefaultShift().name('Dinner').firstSeating(1140).lastSeating(1275).build()]),
                        fetchSchedules: jest.fn().mockResolvedValue([
                            {
                                id: null,
                                rid: 390150,
                                name: null,
                                closed: true,
                                cancelReservationsOnClosure: false,
                                reservationCancellationMessage: null,
                                closedDayMessage: null,
                                date: '2025-10-27',
                                isHoliday: true,
                                isDefaultSchedule: true,
                                shiftNames: ['Dinner'],
                                shiftViews: [
                                    {
                                        name: 'Dinner',
                                        firstSeating: 1140,
                                        lastSeating: 1275,
                                        isCustomShift: false,
                                        recurringShiftDays: {
                                            sunday: true,
                                            monday: true,
                                            tuesday: true,
                                            wednesday: true,
                                            thursday: true,
                                            friday: true,
                                            saturday: true,
                                        },
                                    },
                                ],
                                timestamp: 0,
                            },
                        ]),
                    } as unknown as jest.Mocked<OpenTableProvider>;
                    init({ openTableProviderStub });

                    const restaurantsRepository = container.resolve(RestaurantsRepository);
                    const restaurantId = newDbId();
                    const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms'>({
                        seeds: {
                            restaurants: {
                                data() {
                                    return [
                                        getDefaultRestaurant()
                                            ._id(restaurantId)
                                            .specialHours([
                                                {
                                                    isClosed: false,
                                                    startDate: { year: 2025, month: 9, day: 27 },
                                                    openTime: '19:00',
                                                    endDate: { year: 2025, month: 9, day: 27 },
                                                    closeTime: '21:15',
                                                },
                                            ])
                                            .build(),
                                    ];
                                },
                            },
                            platforms: {
                                data() {
                                    return [
                                        getDefaultPlatform().socialId('123').restaurantId(restaurantId).key(PlatformKey.OPENTABLE).build(),
                                    ];
                                },
                            },
                        },
                        expectedResult() {
                            return {
                                success: true,
                                errors: [],
                            };
                        },
                    });
                    await testCase.build();
                    const restaurant: RestaurantPopulatedToPublish = (await restaurantsRepository.findOne({
                        filter: { _id: toDbId(restaurantId) },
                        options: {
                            lean: true,
                            populate: [
                                { path: 'category' },
                                { path: 'logo' },
                                { path: 'cover' },
                                { path: 'categoryList' },
                                { path: 'attributeList', populate: [{ path: 'attribute' }] },
                            ],
                        },
                    })) as RestaurantPopulatedToPublish;
                    const keysToUpdate: (keyof RestaurantPopulatedToPublish)[] = ['specialHours'];

                    const mockDateProvider = jest.fn().mockReturnValue(new Date('2025-01-01'));
                    dateProvider.provideTodayDate = mockDateProvider;
                    const openTableProvider = container.resolve(OpenTableProvider);
                    const patchRestaurantSpy = jest.spyOn(openTableProvider, 'patchRestaurant');
                    const addSpecialOpenHoursSpy = jest.spyOn(openTableProvider, 'addSpecialOpenHours');
                    const publishingSpecialHoursSpy = jest.spyOn(openTableProvider, 'publishSpecialHoursUpdates');

                    const result = await publishOnOpenTablePlatformUseCase.execute({ restaurant, keysToUpdate });

                    const expectedResult = testCase.getExpectedResult();
                    expect(result).toEqual(expectedResult);
                    expect(patchRestaurantSpy).toHaveBeenCalledTimes(0);
                    expect(addSpecialOpenHoursSpy).toHaveBeenCalledTimes(1);
                    expect(addSpecialOpenHoursSpy).toHaveBeenCalledWith(
                        expect.objectContaining({
                            body: expect.anything(),
                            socialId: '123',
                            specialHourDate: '2025-10-27',
                        })
                    );
                    expect(publishingSpecialHoursSpy).toHaveBeenCalledTimes(1);
                });

                it('should add special hours if matching a shift', async () => {
                    const openTableProviderStub = {
                        patchRestaurant: jest.fn().mockResolvedValue(undefined),
                        addSpecialOpenHours: jest.fn().mockResolvedValue(undefined),
                        publishSpecialHoursUpdates: jest.fn().mockResolvedValue(undefined),
                        fetchShifts: jest.fn().mockResolvedValue([getDefaultShift().firstSeating(1140).lastSeating(1275).build()]),
                        fetchSchedules: jest.fn().mockResolvedValue([]),
                    } as unknown as jest.Mocked<OpenTableProvider>;
                    init({ openTableProviderStub });

                    const restaurantsRepository = container.resolve(RestaurantsRepository);
                    const restaurantId = newDbId();
                    const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms'>({
                        seeds: {
                            restaurants: {
                                data() {
                                    return [
                                        getDefaultRestaurant()
                                            ._id(restaurantId)
                                            .specialHours([
                                                {
                                                    isClosed: false,
                                                    startDate: { year: 2025, month: 9, day: 27 },
                                                    openTime: '19:00',
                                                    endDate: { year: 2025, month: 9, day: 28 },
                                                    closeTime: '21:15',
                                                },
                                            ])
                                            .build(),
                                    ];
                                },
                            },
                            platforms: {
                                data() {
                                    return [
                                        getDefaultPlatform().socialId('123').restaurantId(restaurantId).key(PlatformKey.OPENTABLE).build(),
                                    ];
                                },
                            },
                        },
                        expectedResult() {
                            return {
                                success: true,
                                errors: [],
                            };
                        },
                    });
                    await testCase.build();
                    const restaurant: RestaurantPopulatedToPublish = (await restaurantsRepository.findOne({
                        filter: { _id: toDbId(restaurantId) },
                        options: {
                            lean: true,
                            populate: [
                                { path: 'category' },
                                { path: 'logo' },
                                { path: 'cover' },
                                { path: 'categoryList' },
                                { path: 'attributeList', populate: [{ path: 'attribute' }] },
                            ],
                        },
                    })) as RestaurantPopulatedToPublish;
                    const keysToUpdate: (keyof RestaurantPopulatedToPublish)[] = ['specialHours'];

                    const mockDateProvider = jest.fn().mockReturnValue(new Date('2025-01-01'));
                    dateProvider.provideTodayDate = mockDateProvider;
                    const openTableProvider = container.resolve(OpenTableProvider);
                    const patchRestaurantSpy = jest.spyOn(openTableProvider, 'patchRestaurant');
                    const addSpecialOpenHoursSpy = jest.spyOn(openTableProvider, 'addSpecialOpenHours');
                    const publishingSpecialHoursSpy = jest.spyOn(openTableProvider, 'publishSpecialHoursUpdates');

                    const result = await publishOnOpenTablePlatformUseCase.execute({ restaurant, keysToUpdate });

                    const expectedResult = testCase.getExpectedResult();
                    expect(result).toEqual(expectedResult);
                    expect(patchRestaurantSpy).toHaveBeenCalledTimes(0);
                    expect(addSpecialOpenHoursSpy).toHaveBeenCalledTimes(2);
                    expect(addSpecialOpenHoursSpy).toHaveBeenNthCalledWith(
                        1,
                        expect.objectContaining({
                            body: expect.anything(),
                            socialId: '123',
                            specialHourDate: '2025-10-27',
                        })
                    );
                    expect(addSpecialOpenHoursSpy).toHaveBeenNthCalledWith(
                        2,
                        expect.objectContaining({
                            body: expect.anything(),
                            socialId: '123',
                            specialHourDate: '2025-10-28',
                        })
                    );
                    expect(publishingSpecialHoursSpy).toHaveBeenCalledTimes(1);
                });
            });

            describe('Close special hours', () => {
                it('should close special hours', async () => {
                    const openTableProviderStub = {
                        patchRestaurant: jest.fn().mockResolvedValue(undefined),
                        closeSpecialHours: jest.fn().mockResolvedValue(undefined),
                        publishSpecialHoursUpdates: jest.fn().mockResolvedValue(undefined),
                        fetchSchedules: jest.fn().mockResolvedValue([]),
                    } as unknown as jest.Mocked<OpenTableProvider>;
                    init({ openTableProviderStub });

                    const restaurantsRepository = container.resolve(RestaurantsRepository);
                    const restaurantId = newDbId();
                    const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms'>({
                        seeds: {
                            restaurants: {
                                data() {
                                    return [
                                        getDefaultRestaurant()
                                            ._id(restaurantId)
                                            .specialHours([
                                                {
                                                    isClosed: true,
                                                    startDate: { year: 2025, month: 9, day: 27 },
                                                    endDate: { year: 2025, month: 9, day: 28 },
                                                },
                                            ])
                                            .build(),
                                    ];
                                },
                            },
                            platforms: {
                                data() {
                                    return [
                                        getDefaultPlatform().socialId('123').restaurantId(restaurantId).key(PlatformKey.OPENTABLE).build(),
                                    ];
                                },
                            },
                        },
                        expectedResult() {
                            return {
                                success: true,
                                errors: [],
                            };
                        },
                    });
                    await testCase.build();
                    const restaurant: RestaurantPopulatedToPublish = (await restaurantsRepository.findOne({
                        filter: { _id: toDbId(restaurantId) },
                        options: {
                            lean: true,
                            populate: [
                                { path: 'category' },
                                { path: 'logo' },
                                { path: 'cover' },
                                { path: 'categoryList' },
                                { path: 'attributeList', populate: [{ path: 'attribute' }] },
                            ],
                        },
                    })) as RestaurantPopulatedToPublish;
                    const keysToUpdate: (keyof RestaurantPopulatedToPublish)[] = ['specialHours'];

                    const mockDateProvider = jest.fn().mockReturnValue(new Date('2025-01-01'));
                    dateProvider.provideTodayDate = mockDateProvider;
                    const openTableProvider = container.resolve(OpenTableProvider);
                    const patchRestaurantSpy = jest.spyOn(openTableProvider, 'patchRestaurant');
                    const closeSpecialHoursSpy = jest.spyOn(openTableProvider, 'closeSpecialHours');
                    const publishingSpecialHoursSpy = jest.spyOn(openTableProvider, 'publishSpecialHoursUpdates');

                    const result = await publishOnOpenTablePlatformUseCase.execute({ restaurant, keysToUpdate });

                    const expectedResult = testCase.getExpectedResult();
                    expect(result).toEqual(expectedResult);
                    expect(patchRestaurantSpy).toHaveBeenCalledTimes(0);
                    expect(closeSpecialHoursSpy).toHaveBeenCalledTimes(1);
                    expect(closeSpecialHoursSpy).toHaveBeenCalledWith({
                        openTableSpecialHours: expect.objectContaining({
                            upsertOverrideRequestDTOs: expect.arrayContaining([
                                expect.objectContaining({ date: '2025-10-27' }),
                                expect.objectContaining({ date: '2025-10-28' }),
                            ]),
                        }),
                        socialId: '123',
                    });
                    expect(publishingSpecialHoursSpy).toHaveBeenCalledTimes(1);
                });
            });

            describe('Delete special hours', () => {
                it('should delete closed special hours', async () => {
                    const openTableProviderStub = {
                        patchRestaurant: jest.fn().mockResolvedValue(undefined),
                        deleteSpecialHour: jest.fn().mockResolvedValue(undefined),
                        publishSpecialHoursUpdates: jest.fn().mockResolvedValue(undefined),
                        fetchShifts: jest.fn().mockResolvedValue([]),
                        fetchSchedules: jest.fn().mockResolvedValue([
                            {
                                id: '67d19e783d42021f04918114',
                                rid: 390150,
                                name: null,
                                closed: true,
                                cancelReservationsOnClosure: false,
                                reservationCancellationMessage: null,
                                closedDayMessage: null,
                                date: '2025-10-27',
                                isHoliday: false,
                                isDefaultSchedule: false,
                                shiftNames: [],
                                shiftViews: [],
                                timestamp: 0,
                            },
                            {
                                id: '67d19e783d42021f04918115',
                                rid: 390150,
                                name: null,
                                closed: true,
                                cancelReservationsOnClosure: false,
                                reservationCancellationMessage: null,
                                closedDayMessage: null,
                                date: '2025-10-28',
                                isHoliday: false,
                                isDefaultSchedule: false,
                                shiftNames: [],
                                shiftViews: [],
                                timestamp: 0,
                            },
                        ]),
                    } as unknown as jest.Mocked<OpenTableProvider>;
                    init({ openTableProviderStub });

                    const restaurantsRepository = container.resolve(RestaurantsRepository);
                    const restaurantId = newDbId();
                    const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms'>({
                        seeds: {
                            restaurants: {
                                data() {
                                    return [getDefaultRestaurant()._id(restaurantId).specialHours([]).build()];
                                },
                            },
                            platforms: {
                                data() {
                                    return [
                                        getDefaultPlatform().socialId('123').restaurantId(restaurantId).key(PlatformKey.OPENTABLE).build(),
                                    ];
                                },
                            },
                        },
                        expectedResult() {
                            return {
                                success: true,
                                errors: [],
                            };
                        },
                    });
                    await testCase.build();
                    const restaurant: RestaurantPopulatedToPublish = (await restaurantsRepository.findOne({
                        filter: { _id: toDbId(restaurantId) },
                        options: {
                            lean: true,
                            populate: [
                                { path: 'category' },
                                { path: 'logo' },
                                { path: 'cover' },
                                { path: 'categoryList' },
                                { path: 'attributeList', populate: [{ path: 'attribute' }] },
                            ],
                        },
                    })) as RestaurantPopulatedToPublish;
                    const keysToUpdate: (keyof RestaurantPopulatedToPublish)[] = ['specialHours'];

                    const mockDateProvider = jest.fn().mockReturnValue(new Date('2025-01-01'));
                    dateProvider.provideTodayDate = mockDateProvider;
                    const openTableProvider = container.resolve(OpenTableProvider);
                    const patchRestaurantSpy = jest.spyOn(openTableProvider, 'patchRestaurant');
                    const deleteSpecialHourSpy = jest.spyOn(openTableProvider, 'deleteSpecialHour');
                    const publishSpecialHoursSpy = jest.spyOn(openTableProvider, 'publishSpecialHoursUpdates');

                    const result = await publishOnOpenTablePlatformUseCase.execute({ restaurant, keysToUpdate });

                    const expectedResult = testCase.getExpectedResult();
                    expect(result).toEqual(expectedResult);
                    expect(patchRestaurantSpy).toHaveBeenCalledTimes(0);
                    expect(deleteSpecialHourSpy).toHaveBeenCalledTimes(2);
                    expect(deleteSpecialHourSpy).toHaveBeenNthCalledWith(
                        1,
                        expect.objectContaining({ socialId: '123', scheduleId: '67d19e783d42021f04918114' })
                    );
                    expect(deleteSpecialHourSpy).toHaveBeenNthCalledWith(
                        2,
                        expect.objectContaining({ socialId: '123', scheduleId: '67d19e783d42021f04918115' })
                    );
                    expect(publishSpecialHoursSpy).toHaveBeenCalledTimes(1);
                });

                it('should delete open special hours', async () => {
                    const openTableProviderStub = {
                        patchRestaurant: jest.fn().mockResolvedValue(undefined),
                        deleteSpecialHour: jest.fn().mockResolvedValue(undefined),
                        publishSpecialHoursUpdates: jest.fn().mockResolvedValue(undefined),
                        fetchShifts: jest.fn().mockResolvedValue([]),
                        fetchSchedules: jest.fn().mockResolvedValue([
                            {
                                id: '67d19e783d42021f04918114',
                                rid: 390150,
                                name: null,
                                closed: false,
                                cancelReservationsOnClosure: false,
                                reservationCancellationMessage: null,
                                closedDayMessage: null,
                                date: '2025-10-27',
                                isHoliday: false,
                                isDefaultSchedule: false,
                                shiftNames: ['Saturday Dinner'],
                                shiftViews: [
                                    {
                                        name: 'Saturday Dinner',
                                        firstSeating: 540,
                                        lastSeating: 660,
                                        isCustomShift: false,
                                        recurringShiftDays: {
                                            monday: false,
                                            tuesday: false,
                                            wednesday: false,
                                            thursday: false,
                                            friday: false,
                                            saturday: true,
                                            sunday: false,
                                        },
                                    },
                                ],
                                timestamp: 0,
                            },
                            {
                                id: '67d19e783d42021f04918115',
                                rid: 390150,
                                name: null,
                                closed: true,
                                cancelReservationsOnClosure: false,
                                reservationCancellationMessage: null,
                                closedDayMessage: null,
                                date: '2025-10-28',
                                isHoliday: false,
                                isDefaultSchedule: false,
                                shiftNames: ['Saturday Dinner'],
                                shiftViews: [
                                    {
                                        name: 'Saturday Dinner',
                                        firstSeating: 540,
                                        lastSeating: 660,
                                        isCustomShift: false,
                                        recurringShiftDays: {
                                            monday: false,
                                            tuesday: false,
                                            wednesday: false,
                                            thursday: false,
                                            friday: false,
                                            saturday: true,
                                            sunday: false,
                                        },
                                    },
                                ],
                                timestamp: 0,
                            },
                        ]),
                    } as unknown as jest.Mocked<OpenTableProvider>;
                    init({ openTableProviderStub });

                    const restaurantsRepository = container.resolve(RestaurantsRepository);
                    const restaurantId = newDbId();
                    const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms'>({
                        seeds: {
                            restaurants: {
                                data() {
                                    return [getDefaultRestaurant()._id(restaurantId).specialHours([]).build()];
                                },
                            },
                            platforms: {
                                data() {
                                    return [
                                        getDefaultPlatform().socialId('123').restaurantId(restaurantId).key(PlatformKey.OPENTABLE).build(),
                                    ];
                                },
                            },
                        },
                        expectedResult() {
                            return {
                                success: true,
                                errors: [],
                            };
                        },
                    });
                    await testCase.build();
                    const restaurant: RestaurantPopulatedToPublish = (await restaurantsRepository.findOne({
                        filter: { _id: toDbId(restaurantId) },
                        options: {
                            lean: true,
                            populate: [
                                { path: 'category' },
                                { path: 'logo' },
                                { path: 'cover' },
                                { path: 'categoryList' },
                                { path: 'attributeList', populate: [{ path: 'attribute' }] },
                            ],
                        },
                    })) as RestaurantPopulatedToPublish;
                    const keysToUpdate: (keyof RestaurantPopulatedToPublish)[] = ['specialHours'];

                    const mockDateProvider = jest.fn().mockReturnValue(new Date('2025-01-01'));
                    dateProvider.provideTodayDate = mockDateProvider;
                    const openTableProvider = container.resolve(OpenTableProvider);
                    const patchRestaurantSpy = jest.spyOn(openTableProvider, 'patchRestaurant');
                    const deleteSpecialHourSpy = jest.spyOn(openTableProvider, 'deleteSpecialHour');
                    const publishSpecialHoursSpy = jest.spyOn(openTableProvider, 'publishSpecialHoursUpdates');

                    const result = await publishOnOpenTablePlatformUseCase.execute({ restaurant, keysToUpdate });

                    const expectedResult = testCase.getExpectedResult();
                    expect(result).toEqual(expectedResult);
                    expect(patchRestaurantSpy).toHaveBeenCalledTimes(0);
                    expect(deleteSpecialHourSpy).toHaveBeenCalledTimes(2);
                    expect(deleteSpecialHourSpy).toHaveBeenNthCalledWith(
                        1,
                        expect.objectContaining({ socialId: '123', scheduleId: '67d19e783d42021f04918114' })
                    );
                    expect(deleteSpecialHourSpy).toHaveBeenNthCalledWith(
                        2,
                        expect.objectContaining({ socialId: '123', scheduleId: '67d19e783d42021f04918115' })
                    );
                    expect(publishSpecialHoursSpy).toHaveBeenCalledTimes(1);
                });

                it('should delete close special hours before adding new open special hours', async () => {
                    const openTableProviderStub = {
                        patchRestaurant: jest.fn().mockResolvedValue(undefined),
                        deleteSpecialHour: jest.fn().mockResolvedValue(undefined),
                        addSpecialOpenHours: jest.fn().mockResolvedValue(undefined),
                        publishSpecialHoursUpdates: jest.fn().mockResolvedValue(undefined),
                        fetchShifts: jest.fn().mockResolvedValue([getDefaultShift().firstSeating(700).lastSeating(815).build()]),
                        fetchSchedules: jest.fn().mockResolvedValue([
                            {
                                id: '67d19e783d42021f04918114',
                                rid: 390150,
                                name: null,
                                closed: true,
                                cancelReservationsOnClosure: false,
                                reservationCancellationMessage: null,
                                closedDayMessage: null,
                                date: '2025-10-27',
                                isHoliday: false,
                                isDefaultSchedule: false,
                                shiftNames: [],
                                shiftViews: [],
                                timestamp: 0,
                            },
                        ]),
                    } as unknown as jest.Mocked<OpenTableProvider>;
                    init({ openTableProviderStub });

                    const restaurantsRepository = container.resolve(RestaurantsRepository);
                    const restaurantId = newDbId();
                    const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms'>({
                        seeds: {
                            restaurants: {
                                data() {
                                    return [
                                        getDefaultRestaurant()
                                            ._id(restaurantId)
                                            .specialHours([
                                                {
                                                    isClosed: false,
                                                    startDate: { year: 2025, month: 9, day: 27 },
                                                    openTime: '11:40',
                                                    endDate: { year: 2025, month: 9, day: 29 },
                                                    closeTime: '13:35',
                                                },
                                            ])
                                            .build(),
                                    ];
                                },
                            },
                            platforms: {
                                data() {
                                    return [
                                        getDefaultPlatform().socialId('123').restaurantId(restaurantId).key(PlatformKey.OPENTABLE).build(),
                                    ];
                                },
                            },
                        },
                        expectedResult() {
                            return {
                                success: true,
                                errors: [],
                            };
                        },
                    });
                    await testCase.build();
                    const restaurant: RestaurantPopulatedToPublish = (await restaurantsRepository.findOne({
                        filter: { _id: toDbId(restaurantId) },
                        options: {
                            lean: true,
                            populate: [
                                { path: 'category' },
                                { path: 'logo' },
                                { path: 'cover' },
                                { path: 'categoryList' },
                                { path: 'attributeList', populate: [{ path: 'attribute' }] },
                            ],
                        },
                    })) as RestaurantPopulatedToPublish;
                    const keysToUpdate: (keyof RestaurantPopulatedToPublish)[] = ['specialHours'];

                    const mockDateProvider = jest.fn().mockReturnValue(new Date('2025-01-01'));
                    dateProvider.provideTodayDate = mockDateProvider;
                    const openTableProvider = container.resolve(OpenTableProvider);
                    const deleteSpecialHourSpy = jest.spyOn(openTableProvider, 'deleteSpecialHour');
                    const addSpecialOpenHoursSpy = jest.spyOn(openTableProvider, 'addSpecialOpenHours');
                    const publishingSpecialHoursSpy = jest.spyOn(openTableProvider, 'publishSpecialHoursUpdates');

                    const result = await publishOnOpenTablePlatformUseCase.execute({ restaurant, keysToUpdate });

                    const expectedResult = testCase.getExpectedResult();
                    expect(result).toEqual(expectedResult);
                    expect(deleteSpecialHourSpy).toHaveBeenCalledTimes(1);
                    expect(addSpecialOpenHoursSpy).toHaveBeenCalledTimes(3);
                    expect(publishingSpecialHoursSpy).toHaveBeenCalledTimes(1);
                });

                it('should delete open special hours before closing some special hours', async () => {
                    const openTableProviderStub = {
                        patchRestaurant: jest.fn().mockResolvedValue(undefined),
                        deleteSpecialHour: jest.fn().mockResolvedValue(undefined),
                        closeSpecialHours: jest.fn().mockResolvedValue(undefined),
                        publishSpecialHoursUpdates: jest.fn().mockResolvedValue(undefined),
                        fetchShifts: jest.fn().mockResolvedValue([]),
                        fetchSchedules: jest.fn().mockResolvedValue([
                            {
                                id: '67d19e783d42021f04918114',
                                rid: 390150,
                                name: null,
                                closed: false,
                                cancelReservationsOnClosure: false,
                                reservationCancellationMessage: null,
                                closedDayMessage: null,
                                date: '2025-10-27',
                                isHoliday: false,
                                isDefaultSchedule: false,
                                shiftNames: ['Saturday Lunch'],
                                shiftViews: [
                                    {
                                        name: 'Saturday Lunch',
                                        firstSeating: 540,
                                        lastSeating: 660,
                                        isCustomShift: false,
                                        recurringShiftDays: {
                                            monday: false,
                                            tuesday: false,
                                            wednesday: false,
                                            thursday: false,
                                            friday: false,
                                            saturday: true,
                                            sunday: false,
                                        },
                                    },
                                ],
                                timestamp: 0,
                            },
                        ]),
                    } as unknown as jest.Mocked<OpenTableProvider>;
                    init({ openTableProviderStub });

                    const restaurantsRepository = container.resolve(RestaurantsRepository);
                    const restaurantId = newDbId();
                    const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms'>({
                        seeds: {
                            restaurants: {
                                data() {
                                    return [
                                        getDefaultRestaurant()
                                            ._id(restaurantId)
                                            .specialHours([
                                                {
                                                    isClosed: true,
                                                    startDate: { year: 2025, month: 9, day: 27 },
                                                    endDate: { year: 2025, month: 9, day: 29 },
                                                },
                                            ])
                                            .build(),
                                    ];
                                },
                            },
                            platforms: {
                                data() {
                                    return [
                                        getDefaultPlatform().socialId('123').restaurantId(restaurantId).key(PlatformKey.OPENTABLE).build(),
                                    ];
                                },
                            },
                        },
                        expectedResult() {
                            return {
                                success: true,
                                errors: [],
                            };
                        },
                    });
                    await testCase.build();
                    const restaurant: RestaurantPopulatedToPublish = (await restaurantsRepository.findOne({
                        filter: { _id: toDbId(restaurantId) },
                        options: {
                            lean: true,
                            populate: [
                                { path: 'category' },
                                { path: 'logo' },
                                { path: 'cover' },
                                { path: 'categoryList' },
                                { path: 'attributeList', populate: [{ path: 'attribute' }] },
                            ],
                        },
                    })) as RestaurantPopulatedToPublish;
                    const keysToUpdate: (keyof RestaurantPopulatedToPublish)[] = ['specialHours'];

                    const mockDateProvider = jest.fn().mockReturnValue(new Date('2025-01-01'));
                    dateProvider.provideTodayDate = mockDateProvider;
                    const openTableProvider = container.resolve(OpenTableProvider);
                    const deleteSpecialHourSpy = jest.spyOn(openTableProvider, 'deleteSpecialHour');
                    const closeSpecialHoursSpy = jest.spyOn(openTableProvider, 'closeSpecialHours');
                    const publishingSpecialHoursSpy = jest.spyOn(openTableProvider, 'publishSpecialHoursUpdates');

                    const result = await publishOnOpenTablePlatformUseCase.execute({ restaurant, keysToUpdate });

                    const expectedResult = testCase.getExpectedResult();
                    expect(result).toEqual(expectedResult);
                    expect(deleteSpecialHourSpy).toHaveBeenCalledTimes(1);
                    expect(closeSpecialHoursSpy).toHaveBeenCalledTimes(1);
                    expect(publishingSpecialHoursSpy).toHaveBeenCalledTimes(1);
                });
            });
        });
    });

    const init = ({ openTableProviderStub }: { openTableProviderStub: jest.Mocked<OpenTableProvider> }) => {
        container.clearInstances();

        registerRepositories(['CategoriesRepository', 'RestaurantsRepository', 'PlatformsRepository']);

        dateProvider = container.resolve(DateProviderPort);

        container.registerInstance(OpenTableProvider, openTableProviderStub);

        publishOnOpenTablePlatformUseCase = container.resolve(PublishOnOpenTablePlatformUseCase);
    };
});
