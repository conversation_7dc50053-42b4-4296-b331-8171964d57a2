import { partition, pick, uniq } from 'lodash';
import { DateTime } from 'luxon';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import {
    createDateFromMalouDate,
    filterByRequired<PERSON><PERSON>s,
    getDatesBetween,
    isNotNil,
    MalouErrorCode,
    PlatformKey,
    TimeInMilliseconds,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { DateProviderPort } from ':helpers/providers/date/date.provider';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { OpenTableMapper } from ':modules/platforms/platforms/opentable/opentable-mapper';
import {
    PublishOnPlatformResponse,
    RestaurantPopulatedToPublish,
    RestaurantToPublishKey,
} from ':modules/platforms/use-cases/publish-on-connected-platforms/publish-on-connected-platforms.use-case';
import { Shift } from ':modules/shifts/shift';
import {
    OpenTableScheduleGetResult,
    OpenTableScheduleShift,
    OpenTableScheduleShiftType,
} from ':providers/opentable/interfaces/opentable.schedules.interface';
import { OpenTableShift } from ':providers/opentable/interfaces/opentable.shifts.interface';
import { OpenTableProvider } from ':providers/opentable/opentable.provider';

@singleton()
export class PublishOnOpenTablePlatformUseCase {
    constructor(
        private readonly _openTableMapper: OpenTableMapper,
        private readonly _openTableProvider: OpenTableProvider,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _dateProvider: DateProviderPort
    ) {}

    async execute({
        restaurant,
        keysToUpdate,
    }: {
        restaurant: RestaurantPopulatedToPublish;
        keysToUpdate: RestaurantToPublishKey[];
    }): Promise<PublishOnPlatformResponse> {
        const restaurantId = restaurant._id.toString();
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.OPENTABLE);

        if (!platform) {
            logger.warn('[OPENTABLE PUBLISH] Platform not found', { restaurantId });
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND);
        }
        const socialId = platform.socialId;
        assert(socialId, 'Missing socialId on platform');

        const hasSpecialHoursKey = keysToUpdate.includes('specialHours');

        const openTableKeysToUpdate = this._getOpenTableKeysToUpdate(keysToUpdate);

        const errors: PublishOnPlatformResponse['errors'] = [];
        try {
            // We always have _id in keysToUpdate
            if (openTableKeysToUpdate.length > 1) {
                const restaurantToUpdate = pick(restaurant, openTableKeysToUpdate);
                const openTableRestaurant = this._openTableMapper.toPlatformMapper({
                    ...restaurantToUpdate,
                    id: restaurantId,
                });
                await this._openTableProvider.patchRestaurant({ socialId, openTableRestaurant });
            }
            if (hasSpecialHoursKey) {
                try {
                    await this._updateSpecialHours({ socialId, specialHours: restaurant.specialHours });
                } catch (error) {
                    logger.error('[OPENTABLE PUBLISH] Error while updating special hours on OpenTable', {
                        restaurantId,
                        error,
                    });
                    errors.push({
                        field: 'specialHours',
                        reason: error instanceof Error ? error.message : 'Unknown error',
                    });
                }
            }
        } catch (error) {
            logger.error('[OPENTABLE PUBLISH] Error while publishing on OpenTable', {
                restaurantId,
                error,
            });
            errors.push({
                field: openTableKeysToUpdate.join(', '),
                reason: error instanceof Error ? error.message : 'Unknown error',
            });
            throw error;
        }
        return {
            success: errors.length === 0,
            errors,
        };
    }

    private _getOpenTableKeysToUpdate(keysToUpdate: RestaurantToPublishKey[]): RestaurantToPublishKey[] {
        // We filter out 'specialHours' because it is handled separately
        const keys = keysToUpdate.filter((key) => key !== 'specialHours');
        return uniq([
            ...keys,
            // When updating the category, we also need to update the categoryList
            ...((keys.includes('category') ? ['categoryList'] : []) as RestaurantToPublishKey[]),
            '_id',
        ]);
    }

    private async _updateSpecialHours({
        socialId,
        specialHours,
    }: {
        socialId: string;
        specialHours: RestaurantPopulatedToPublish['specialHours'];
    }): Promise<void> {
        const schedulesUntilLastSpecialHoursDate = await this._fetchSchedulesUntilLastSpecialHourDate({ socialId, specialHours });
        const { closedSpecialHours, deleteSpecialHourDateWithSchedule, newOpenSpecialHours } = this._partitionAndFilterSpecialHours(
            specialHours,
            schedulesUntilLastSpecialHoursDate
        );

        let specialHoursUpdated = 0;
        if (deleteSpecialHourDateWithSchedule.length > 0) {
            specialHoursUpdated += await this._deleteSpecialHours({ socialId, dateWithSchedule: deleteSpecialHourDateWithSchedule });
        }
        if (closedSpecialHours.length > 0) {
            specialHoursUpdated += await this._closeSpecialHours({ socialId, closedSpecialHours });
        }
        if (newOpenSpecialHours.length > 0) {
            specialHoursUpdated += await this._addNewSpecialOpenHours({
                socialId,
                newOpenSpecialHours,
                schedules: schedulesUntilLastSpecialHoursDate,
            });
        }

        if (specialHoursUpdated > 0) {
            await this._openTableProvider.publishSpecialHoursUpdates({ socialId });
        }
    }

    private async _fetchSchedulesUntilLastSpecialHourDate({
        socialId,
        specialHours,
    }: {
        socialId: string;
        specialHours: RestaurantPopulatedToPublish['specialHours'];
    }): Promise<OpenTableScheduleGetResult[]> {
        const startDate = this._dateProvider.provideTodayDate();
        const lastSpecialHourDate = specialHours.reduce(
            (latest: Date, currentSpecialHour: RestaurantPopulatedToPublish['specialHours'][0]) => {
                const currentDate = createDateFromMalouDate(currentSpecialHour.endDate);
                return currentDate > latest ? currentDate : latest;
            },
            DateTime.fromJSDate(startDate).plus({ months: 12 }).toJSDate()
        );
        return this._openTableProvider.fetchSchedules({ socialId, startDate, endDate: lastSpecialHourDate });
    }

    /**
     * Merges the logic of filtering and partitioning special hours to provide access to the date in partitioned results.
     * Returns partitioned special hours with date context, only including those that need to be updated.
     */
    private _partitionAndFilterSpecialHours(
        specialHours: RestaurantPopulatedToPublish['specialHours'],
        schedules: OpenTableScheduleGetResult[]
    ): {
        closedSpecialHours: { date: Date; specialHour: RestaurantPopulatedToPublish['specialHours'][0] }[];
        deleteSpecialHourDateWithSchedule: { date: Date; scheduleId: string; scheduleTimestamp: number }[];
        newOpenSpecialHours: { date: Date; specialHour: RestaurantPopulatedToPublish['specialHours'][0] }[];
    } {
        const today = this._dateProvider.provideTodayDate();
        // Flatten special hours with their date context, only those that need update
        const specialHoursWithDate = specialHours
            .flatMap((specialHour) => {
                const startDate = createDateFromMalouDate(specialHour.startDate);
                const endDate = createDateFromMalouDate(specialHour.endDate);
                const oneDayAfterEndDate = new Date(endDate.getTime() + TimeInMilliseconds.DAY);
                const dates = getDatesBetween(startDate, oneDayAfterEndDate);
                return dates
                    .map((date) => {
                        if (!date || date < today) {
                            return null;
                        }
                        return { date, specialHour };
                    })
                    .filter(isNotNil);
            })
            .filter(({ date, specialHour }) => {
                const isoDate = DateTime.fromJSDate(date).toISODate();
                const foundSchedule = schedules.find((schedule) => schedule.date === isoDate);
                if (!foundSchedule) {
                    return true;
                }
                const areClosedTheSame = foundSchedule.closed === specialHour.isClosed;
                const isSpecialHourInSchedule = foundSchedule.shiftViews.some((openTableShift) => {
                    const shift = Shift.fromOpenTable(openTableShift);
                    return shift.startTime === specialHour.openTime && shift.endTime === specialHour.closeTime;
                });
                return !(areClosedTheSame && isSpecialHourInSchedule);
            });

        // Partition closed/open
        const [closedSpecialHours, openSpecialHours] = partition(specialHoursWithDate, ({ specialHour }) => specialHour.isClosed);
        // Delete not default schedules that has no matching special hour for the same day
        const deleteSpecialHourDateWithSchedule: { date: Date; scheduleId: string; scheduleTimestamp: number }[] = filterByRequiredKeys(
            schedules
                .filter((schedule) => {
                    if (schedule.isDefaultSchedule) {
                        return false;
                    }
                    const shifts = schedule.shiftViews.map((shift) => Shift.fromOpenTable(shift));
                    const scheduleDate = DateTime.fromISO(schedule.date).toJSDate();
                    const hasMatchingClosedSpecialHour = closedSpecialHours.some(({ date }) => date === scheduleDate);
                    const hasMatchingOpenSpecialHour = openSpecialHours.some(({ date, specialHour }) => {
                        return (
                            date === scheduleDate &&
                            shifts.some((shift) => shift.startTime === specialHour.openTime && shift.endTime === specialHour.closeTime)
                        );
                    });
                    return !hasMatchingClosedSpecialHour && !hasMatchingOpenSpecialHour;
                })
                .map((schedule) => ({
                    date: DateTime.fromISO(schedule.date).toJSDate(),
                    scheduleId: schedule.id,
                    scheduleTimestamp: schedule.timestamp,
                })),
            ['scheduleId']
        );

        return {
            closedSpecialHours: closedSpecialHours,
            deleteSpecialHourDateWithSchedule,
            newOpenSpecialHours: openSpecialHours,
        };
    }

    private async _closeSpecialHours({
        socialId,
        closedSpecialHours,
    }: {
        socialId: string;
        closedSpecialHours: { date: Date; specialHour: RestaurantPopulatedToPublish['specialHours'][0] }[];
    }): Promise<number> {
        const openTableSpecialHours = this._openTableMapper.toOpenTableSpecialHours(closedSpecialHours);
        await this._openTableProvider.closeSpecialHours({ socialId, openTableSpecialHours });
        return closedSpecialHours.length;
    }

    private async _deleteSpecialHours({
        socialId,
        dateWithSchedule,
    }: {
        socialId: string;
        dateWithSchedule: { date: Date; scheduleId: string; scheduleTimestamp: number }[];
    }): Promise<number> {
        let shiftDeleted = 0;
        for (const { date, scheduleId, scheduleTimestamp } of dateWithSchedule) {
            const specialHourDate = DateTime.fromJSDate(date).toISODate();
            await this._openTableProvider.deleteSpecialHour({ socialId, specialHourDate, scheduleId, scheduleTimestamp });
            shiftDeleted++;
        }
        return shiftDeleted;
    }

    private async _addNewSpecialOpenHours({
        socialId,
        newOpenSpecialHours,
        schedules,
    }: {
        socialId: string;
        newOpenSpecialHours: { date: Date; specialHour: RestaurantPopulatedToPublish['specialHours'][0] }[];
        schedules: OpenTableScheduleGetResult[];
    }): Promise<number> {
        const shifts = await this._openTableProvider.fetchShifts({ socialId });
        let shiftAdded = 0;
        for (const newOpenSpecialHour of newOpenSpecialHours) {
            const newShifts = this._getNewShifts(newOpenSpecialHour, schedules, shifts);
            if (newShifts.length === 0) {
                continue;
            }

            const specialHourDate = DateTime.fromJSDate(newOpenSpecialHour.date).toISODate();
            const body = this._openTableMapper.toOpenTableNewSpecialHour(socialId, specialHourDate, newShifts);
            await this._openTableProvider.addSpecialOpenHours({
                socialId,
                specialHourDate,
                body,
            });
            shiftAdded++;
        }
        return shiftAdded;
    }

    private _getNewShifts(
        newOpenSpecialHour: { date: Date; specialHour: RestaurantPopulatedToPublish['specialHours'][0] },
        schedules: OpenTableScheduleGetResult[],
        shifts: OpenTableShift[]
    ): OpenTableScheduleShift[] {
        const date = DateTime.fromJSDate(newOpenSpecialHour.date).toISODate();
        const matchingShift = shifts.find((openTableShift) => {
            const shift = Shift.fromOpenTable(openTableShift);
            return (
                shift.startTime === newOpenSpecialHour.specialHour.openTime && shift.endTime === newOpenSpecialHour.specialHour.closeTime
            );
        });
        if (!matchingShift) {
            logger.warn('[OPENTABLE PUBLISH] No matching shift found for special hour, skipping', {
                date,
                specialHour: newOpenSpecialHour.specialHour,
            });
            return [];
        }

        const foundSchedule = schedules.find((schedule) => schedule.date === date);
        const dailyShiftNames = foundSchedule?.shiftViews.map((shiftView) => shiftView.name) || [];
        const dailyShifts = shifts.filter((shift) => dailyShiftNames.includes(shift.name));

        if (foundSchedule && foundSchedule.isHoliday && foundSchedule.closed) {
            // if matching shift is the same in the schedule
            if (dailyShiftNames.includes(matchingShift.name)) {
                return [
                    ...dailyShifts.map((shift) => ({
                        masterShiftId: shift.id,
                        masterShift: shift,
                        customShift: null,
                        shiftType: OpenTableScheduleShiftType.UNCHANGED,
                    })),
                ];
            }
            return [
                {
                    customShift: matchingShift,
                    shiftType: OpenTableScheduleShiftType.NEW,
                },
            ];
        }

        const isMatchingShiftOverlappingDailyShifts = this._checkShiftOverlap(dailyShifts, matchingShift);
        if (isMatchingShiftOverlappingDailyShifts) {
            logger.warn('[OPENTABLE PUBLISH] Matching Shift is overlapping an existing shift, skipping', {
                date,
                specialHour: newOpenSpecialHour.specialHour,
            });
            return [];
        }

        return [
            ...dailyShifts.map((shift) => ({
                masterShiftId: shift.id,
                masterShift: shift,
                customShift: null,
                shiftType: OpenTableScheduleShiftType.UNCHANGED,
            })),
            {
                customShift: matchingShift,
                shiftType: OpenTableScheduleShiftType.NEW,
            },
        ];
    }

    private _checkShiftOverlap(dailyShifts: OpenTableShift[], matchingShift: OpenTableShift): boolean {
        return dailyShifts.some((shift) => {
            const isMatchingShiftStartInsideAShift =
                matchingShift.firstSeating >= shift.firstSeating && matchingShift.firstSeating < shift.lastSeating;
            const isMatchingShiftEndInsideAShift =
                matchingShift.lastSeating > shift.firstSeating && matchingShift.lastSeating <= shift.lastSeating;
            const isMatchingShiftBroaderThanAShift =
                matchingShift.firstSeating <= shift.firstSeating && matchingShift.lastSeating >= shift.lastSeating;
            return isMatchingShiftStartInsideAShift || isMatchingShiftEndInsideAShift || isMatchingShiftBroaderThanAShift;
        });
    }
}
