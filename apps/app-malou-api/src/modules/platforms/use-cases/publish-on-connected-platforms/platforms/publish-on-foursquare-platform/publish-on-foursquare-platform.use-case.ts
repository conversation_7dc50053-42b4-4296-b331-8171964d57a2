import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { FoursquareCredentialsUseCases } from ':modules/credentials/platforms/foursquare/foursquare.use-cases';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { FoursquareMapper } from ':modules/platforms/platforms/foursquare/foursquare-mapper';
import {
    PublishOnPlatformResponse,
    RestaurantPopulatedToPublish,
} from ':modules/platforms/use-cases/publish-on-connected-platforms/publish-on-connected-platforms.use-case';

@singleton()
export class PublishOnFoursquarePlatformUseCase {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _foursquareCredentialsUseCases: FoursquareCredentialsUseCases
    ) {}

    async execute({ restaurant }: { restaurant: RestaurantPopulatedToPublish }): Promise<PublishOnPlatformResponse> {
        try {
            const restaurantId = restaurant._id.toString();
            const mapper = new FoursquareMapper();
            const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.FOURSQUARE);

            if (!platform) {
                logger.warn('[FOURSQUARE PUBLISH] Platform not found', { restaurantId });
                throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND);
            }

            const mappedData = mapper.toPlatformMapper(restaurant);
            const platformSocialId = platform.socialId;
            assert(platformSocialId, 'Missing socialId on platform');
            const response = await this._foursquareCredentialsUseCases.patchLocation({ socialId: platformSocialId, data: mappedData });

            return response.data;
        } catch (error: any) {
            if (error.response?.data) {
                // foursquare error idk what to do with this shit (6 = nb of times this fucked me)
                throw error.response.data.error;
            }

            throw error;
        }
    }
}
