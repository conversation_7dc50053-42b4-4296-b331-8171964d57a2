import { isNil, pick } from 'lodash';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { logger } from ':helpers/logger';
import { AppleBusinessConnectValidationReportSeverity } from ':modules/platforms/platforms/apple-business-connect/apple-business-connect.interfaces';
import { AppleBusinessConnectMapperService } from ':modules/platforms/platforms/apple-business-connect/apple-business-connect.mapper';
import {
    PublishOnPlatformResponse,
    RestaurantPopulatedToPublish,
} from ':modules/platforms/use-cases/publish-on-connected-platforms/publish-on-connected-platforms.use-case';
import AppleBusinessConnectApiProvider from ':providers/apple-business-connect/apple-business-connect.provider';

@singleton()
export class PublishOnAppleBusinessConnectPlatformUseCase {
    constructor(
        private readonly _appleBusinessConnectMapperService: AppleBusinessConnectMapperService,
        private readonly _appleBusinessConnectApiProvider: AppleBusinessConnectApiProvider
    ) {}

    async execute({
        restaurant,
        keysToUpdate,
    }: {
        restaurant: RestaurantPopulatedToPublish;
        keysToUpdate: (keyof RestaurantPopulatedToPublish)[];
    }): Promise<PublishOnPlatformResponse> {
        assert(restaurant.appleBusinessConnect?.locationId);

        // Get Location details
        const locationId = restaurant.appleBusinessConnect.locationId;
        const location = await this._appleBusinessConnectApiProvider.getLocationById({ locationId });

        // Generate new details according to restaurant data
        const newLocationDetails = await this._appleBusinessConnectMapperService.execute({ restaurant, keysToUpdate });

        // Update information and location assets if logo or cover changed
        const { validationReports } = await this._appleBusinessConnectApiProvider.updateLocation({
            locationId,
            etag: location.etag,
            data: {
                id: location.id,
                locationDetails: {
                    ...location.locationDetails,
                    ...newLocationDetails,
                },
            },
        });
        const isExistingViolation = validationReports?.find(
            ({ severity }) => severity === AppleBusinessConnectValidationReportSeverity.VIOLATION
        );
        const success = isNil(isExistingViolation);

        if (!success) {
            logger.error('[ABC PUBLISH] Error publishing infos on ABC', {
                validationReports,
                data: newLocationDetails,
                originalData: pick(restaurant, keysToUpdate),
            });
        }

        return {
            success,
        };
    }
}
