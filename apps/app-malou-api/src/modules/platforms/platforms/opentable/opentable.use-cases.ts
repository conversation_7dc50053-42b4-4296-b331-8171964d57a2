import { singleton } from 'tsyringe';

import { ID } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { PlatformUseCases } from ':modules/platforms/platforms.interface';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { OpenTableMalouData, OpenTableMapper } from ':modules/platforms/platforms/opentable/opentable-mapper';
import { OpenTableRestaurant } from ':providers/opentable/interfaces/opentable.restaurant.interface';
import { OpenTableProvider } from ':providers/opentable/opentable.provider';

@singleton()
export default class OpenTablePlatformsUseCases implements PlatformUseCases {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _openTableProvider: OpenTableProvider
    ) {}

    async getOverviewData({ restaurantId }: { restaurantId: string }) {
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.OPENTABLE);
        if (!platform || !platform.socialId) {
            return { error: true, message: MalouErrorCode.PLATFORM_NOT_FOUND };
        }

        const { socialId } = platform;
        const platformData = await this._openTableProvider.getRestaurant({ socialId });
        return platformData;
    }

    async mapOverviewDataToMalou(platformData: OpenTableRestaurant): Promise<OpenTableMalouData> {
        const mapper = new OpenTableMapper();
        const malouData = mapper.toMalouMapper(platformData);
        return malouData;
    }

    getSocialId(_restaurantId: string): Promise<any> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, { message: 'OpenTablePlatformsUseCases does not implement getSocialId !' });
    }

    async upsertLinkedComponents() {
        // do nothing
    }

    scrapPlatformEndpoint(_endpoint: string): Promise<any> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'OpenTablePlatformsUseCases does not implement scrapPlatformEndpoint !',
        });
    }
    getLocationData(_params: { credentialId?: ID; socialId?: string; apiEndpointV2?: string }): Promise<any> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'OpenTablePlatformsUseCases does not implement getLocationData !',
        });
    }
    getProfileAndCoverMedia(_params: { credentialId?: ID; socialId?: string }): Promise<any> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'OpenTablePlatformsUseCases does not implement getProfileAndCoverMedia !',
        });
    }
}
