import { uniq } from 'lodash';
import { DateTime } from 'luxon';
import { DeepPartial } from 'utility-types';

import { IPlatform } from '@malou-io/package-models';
import {
    ApplicationLanguage,
    CountryCode,
    Day,
    DescriptionSize,
    filterByRequiredKeys,
    isNotNil,
    NonNullableKeys,
} from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { mapToMalouPhone } from ':helpers/utils';
import { gmbCategoriesToOpenTableCategories } from ':modules/categories/services/map-gmb-categories-to-manual-platform-categories/mappings/gmb-to-opentable.mapping';
import { RestaurantPopulatedToPublish } from ':modules/platforms/use-cases/publish-on-connected-platforms/publish-on-connected-platforms.use-case';
import { OpenTableLocale } from ':providers/opentable/interfaces/opentable.locale.interface';
import {
    OpenTableCustomMessage,
    OpenTableDay,
    OpenTableLocalizedMessage,
    OpenTableMessageSpecification,
    OpenTableRestaurant,
    OpentableSpecialHours,
    OpenTableSpecificationDetail,
} from ':providers/opentable/interfaces/opentable.restaurant.interface';
import { OpenTableNewSchedule, OpenTableScheduleShift } from ':providers/opentable/interfaces/opentable.schedules.interface';

export type OpenTableMalouData = Pick<IPlatform, 'name' | 'address' | 'phone' | 'menuUrl' | 'website'>;

/**
 * Mapper implementation for OpenTable
 */
export class OpenTableMapper {
    private MAXIMUM_OTHER_CUISINE_IDS = 2;

    toMalouMapper(platData: OpenTableRestaurant): OpenTableMalouData {
        const {
            content: { restaurantLocal },
            core: { restaurant },
        } = platData;
        const localeData = Object.values(restaurantLocal)[0];
        return {
            name: localeData.restaurantName,
            address: {
                country: restaurant.countryCode,
                regionCode: localeData.state ?? undefined,
                locality: localeData.city,
                postalCode: restaurant.postCode ?? undefined,
                formattedAddress: localeData.address1,
            },
            phone: mapToMalouPhone(restaurant.phoneNumber, restaurant.countryCode as unknown as CountryCode),
            menuUrl: restaurant.menuUrl ?? undefined,
            website: restaurant.url ?? undefined,
        };
    }

    /**
     * According to https://docs.google.com/spreadsheets/d/1RVO-KqtkFU1w71pGBvEI6NJu_budU5ie/edit?gid=29095953#gid=29095953
     * We can only update :
     * - Phone number
     * - Website
     * - Description
     * - Menu URL
     * - Hours
     * - Special hours
     * - Categories
     */
    toPlatformMapper(restaurant: Partial<RestaurantPopulatedToPublish> & { id: string }): DeepPartial<OpenTableRestaurant> {
        const restaurantDescriptionsByLocale = this._getLocationDescriptions(restaurant);
        const website = restaurant.website;
        const menuUrl = restaurant.menuUrl;
        const phoneNumber = this._getOpenTablePhoneNumber(restaurant.phone);
        const primaryCuisineId = this._getOpenTablePrimaryCuisineId(restaurant);
        const otherCuisines = this._getOpenTableOtherCuisineIds(restaurant);
        const hoursSpecifications = this._getHoursMessageSpecification(restaurant);
        const updateTime = new Date().toISOString();

        return {
            ...(restaurantDescriptionsByLocale && {
                content: {
                    customMessages: {
                        RestaurantDescription: {
                            ...restaurantDescriptionsByLocale,
                        },
                    },
                },
            }),
            core: {
                ...((website || menuUrl || phoneNumber) && {
                    restaurant: {
                        ...(website && { url: website }),
                        ...(menuUrl && { menuUrl }),
                        ...(phoneNumber && { phoneNumber }),
                    },
                }),
                ...(primaryCuisineId && {
                    foodTypes: {
                        primaryCuisineId,
                        // OtherCuisines depends on primaryCuisineId because
                        // if primaryCuisineId is not set, we don't want to send otherCuisines
                        ...(primaryCuisineId && otherCuisines && { otherCuisines }),
                    },
                }),
                ...(hoursSpecifications && { messageSpecifications: [hoursSpecifications] }),
                restaurantFeatures: {
                    ProfileUpdateTimes: {
                        Details: updateTime,
                        ...(restaurantDescriptionsByLocale && { Description: updateTime }),
                        ...(hoursSpecifications && { BusinessHours: updateTime }),
                    },
                },
            },
        };
    }

    toOpenTableSpecialHours(
        specialHours: { date: Date; specialHour: RestaurantPopulatedToPublish['specialHours'][0] }[]
    ): OpentableSpecialHours {
        return {
            upsertOverrideRequestDTOs: specialHours.map(({ date, specialHour }) => ({
                id: null,
                name: '',
                closed: specialHour.isClosed,
                shifts: [],
                isHoliday: false,
                cancelReservationsOnClosure: false,
                reservationCancellationMessage: null,
                closedDayMessage: null,
                date: DateTime.fromJSDate(date).toISODate(),
            })),
            deleteOverrideRequestDTOs: [],
        };
    }

    toOpenTableNewSpecialHour(socialId: string, date: string, shifts: OpenTableScheduleShift[]): OpenTableNewSchedule {
        const dayOfWeek = DateTime.fromISO(date).weekday;
        return {
            errorCodes: null,
            creationDate: '0001-01-01',
            createdBy: null,
            lastUpdatedDate: '0001-01-01',
            lastUpdatedBy: null,
            name: '',
            rid: Number(socialId),
            closed: false,
            cancelReservationsOnClosure: false,
            reservationCancellationMessage: null,
            closedDayMessage: null,
            date,
            dayOfWeek,
            isHoliday: false,
            shifts,
            id: null,
            timestamp: 0,
        };
    }

    private _getLocationDescriptions(
        restaurant: Partial<RestaurantPopulatedToPublish> & { id: string }
    ): OpenTableCustomMessage | undefined {
        const { descriptions } = restaurant;
        if (!descriptions || descriptions.length === 0) {
            logger.warn('[OpenTable] No descriptions found for restaurant');
            return undefined;
        }

        const descriptionsWithText = filterByRequiredKeys(descriptions, ['text', 'language']);
        if (descriptionsWithText.length === 0) {
            logger.warn('[OpenTable] No computed descriptions for restaurant', {
                restaurantId: restaurant.id,
                descriptions,
            });

            return undefined;
        }

        const descriptionLanguages = uniq(descriptionsWithText.map((description) => description.language)).filter(isNotNil);
        if (descriptionLanguages.length === 0) {
            return undefined;
        }

        const locationDescriptions = descriptionLanguages.reduce((acc, descriptionLanguage) => {
            const bestDescription = this._getBestDescriptionByLanguage(descriptionsWithText, descriptionLanguage);
            if (bestDescription) {
                const openTableLocale = this._mapApplicationLanguageToOpenTableLocale(descriptionLanguage);
                const messageDescription: OpenTableLocalizedMessage = {
                    message: bestDescription,
                };
                acc[openTableLocale] = messageDescription;
            }
            return acc;
        }, {});

        return locationDescriptions;
    }

    private _getBestDescriptionByLanguage(
        descriptions: NonNullableKeys<RestaurantPopulatedToPublish['descriptions'][0], 'text' | 'language'>[],
        language: string
    ): string {
        const descriptionsByLanguage = descriptions.filter((description) => description.language === language);
        const longDescription = descriptionsByLanguage.find((description) => description.size === DescriptionSize.LONG);

        if (longDescription) {
            return longDescription.text;
        }

        // Else, return first description
        return descriptionsByLanguage[0].text;
    }

    private _mapApplicationLanguageToOpenTableLocale(language: ApplicationLanguage): OpenTableLocale {
        switch (language) {
            case ApplicationLanguage.FR:
                return OpenTableLocale.FR_FR;
            case ApplicationLanguage.ES:
                return OpenTableLocale.ES_ES;
            case ApplicationLanguage.IT:
                return OpenTableLocale.IT_IT;
            case ApplicationLanguage.EN:
            default:
                return OpenTableLocale.EN_US; // Default to English if not specified
        }
    }

    private _getOpenTablePhoneNumber(phone?: { prefix?: number | null; digits?: number | null } | null): string | undefined {
        if (!phone || (!phone.prefix && !phone.digits)) {
            return undefined;
        }
        return `+${phone.prefix}${phone.digits}`;
    }

    private _getOpenTablePrimaryCuisineId(restaurant: Partial<RestaurantPopulatedToPublish> & { id: string }): string | undefined {
        const gmbCategory = restaurant.category;
        if (!gmbCategory || !gmbCategory.categoryId) {
            return undefined;
        }
        const primaryCuisineId = gmbCategoriesToOpenTableCategories[gmbCategory?.categoryId];
        if (primaryCuisineId) {
            return primaryCuisineId;
        }
        if (!restaurant.categoryList || restaurant.categoryList.length === 0) {
            return undefined;
        }
        return restaurant.categoryList
            .map((category) => gmbCategoriesToOpenTableCategories[category.categoryId])
            .filter(isNotNil)
            .shift();
    }

    private _getOpenTableOtherCuisineIds(restaurant: Partial<RestaurantPopulatedToPublish> & { id: string }): string[] {
        const gmbCategories = restaurant.categoryList;
        if (!gmbCategories || gmbCategories.length === 0) {
            return [];
        }

        const primaryCuisineId = this._getOpenTablePrimaryCuisineId(restaurant);
        return uniq(
            gmbCategories
                .map((category) => gmbCategoriesToOpenTableCategories[category.categoryId])
                .filter((cuisineId) => primaryCuisineId !== cuisineId)
                .filter(isNotNil)
                .splice(0, this.MAXIMUM_OTHER_CUISINE_IDS)
        );
    }

    private _getHoursMessageSpecification(restaurant: Partial<RestaurantPopulatedToPublish>): OpenTableMessageSpecification | undefined {
        const specificationDetails = this._getHoursSpecificationDetails(restaurant);
        if (specificationDetails.length === 0) {
            return undefined;
        }
        return {
            Type: 'Hours',
            Specification: {
                schemaVersion: 1,
                specification: specificationDetails,
            },
        };
    }

    private _getHoursSpecificationDetails(restaurant: Partial<RestaurantPopulatedToPublish>): OpenTableSpecificationDetail[] {
        if (!restaurant.regularHours || restaurant.regularHours.length === 0) {
            return [];
        }
        const periods = filterByRequiredKeys(restaurant.regularHours, ['openTime', 'closeTime', 'openDay']).reduce(
            (acc, regularHour) => {
                const key = [regularHour.openTime, regularHour.closeTime].join('-');
                if (!acc[key]) {
                    const newSpecificationDetail: OpenTableSpecificationDetail = {
                        shiftStarts: regularHour.openTime,
                        shiftEnds: regularHour.closeTime,
                        days: [],
                        standardShiftName: {
                            id: 'openingtimes',
                        },
                    };
                    acc[key] = newSpecificationDetail;
                }
                acc[key].days.push(this._mapMalouDayToOpenTableDay(regularHour.openDay));
                return acc;
            },
            {} as Record<string, OpenTableSpecificationDetail>
        );
        return Object.values(periods);
    }

    private _mapMalouDayToOpenTableDay(day: Day): OpenTableDay {
        const map = {
            [Day.MONDAY]: OpenTableDay.MONDAY,
            [Day.TUESDAY]: OpenTableDay.TUESDAY,
            [Day.WEDNESDAY]: OpenTableDay.WEDNESDAY,
            [Day.THURSDAY]: OpenTableDay.THURSDAY,
            [Day.FRIDAY]: OpenTableDay.FRIDAY,
            [Day.SATURDAY]: OpenTableDay.SATURDAY,
            [Day.SUNDAY]: OpenTableDay.SUNDAY,
        };
        return map[day];
    }
}
