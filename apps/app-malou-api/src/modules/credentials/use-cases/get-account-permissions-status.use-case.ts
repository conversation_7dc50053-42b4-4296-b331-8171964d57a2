import { singleton } from 'tsyringe';

import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { GetDeliverooAccountPermissionsStatusUseCase } from ':modules/credentials/platforms/deliveroo/use-cases/get-deliveroo-account-permissions-status.use-case';
import { GetFacebookAccountPermissionsStatusUseCase } from ':modules/credentials/platforms/facebook/use-cases/get-facebook-account-permissions-status.use-case';
import { GetGmbAccountPermissionsStatusUseCase } from ':modules/credentials/platforms/gmb/use-cases/get-gmb-account-permissions-status.use-case';
import { CredentialValidityStatus } from ':modules/credentials/platforms/interfaces';
import { GetOpenTableAccountPermissionsStatusUseCase } from ':modules/credentials/platforms/opentable/use-cases/get-opentable-account-permissions-status.use-case';
import { GetTiktokAccountPermissionsStatusUseCase } from ':modules/credentials/platforms/tiktok/use-cases/get-tiktok-account-permissions-status.use-case';
import { GetTripadvisorAccountPermissionsStatusUseCase } from ':modules/credentials/platforms/tripadvisor/use-cases/get-tripadvisor-account-permissions-status.use-case';
import { GetUbereatsAccountPermissionsStatusUseCase } from ':modules/credentials/platforms/ubereats/use-cases/get-ubereats-account-permissions-status.use-case';
import { GetZenchefAccountPermissionsStatusUseCase } from ':modules/credentials/platforms/zenchef/use-cases/get-zenchef-account-permissions-status.use-case';
import { Platform } from ':modules/platforms/platforms.entity';

@singleton()
export class GetAccountPermissionsStatusUseCase {
    constructor(
        private readonly _getDeliverooAccountPermissionsStatusUseCase: GetDeliverooAccountPermissionsStatusUseCase,
        private readonly _getFacebookAccountPermissionsStatusUseCase: GetFacebookAccountPermissionsStatusUseCase,
        private readonly _getGmbAccountPermissionsStatusUseCase: GetGmbAccountPermissionsStatusUseCase,
        private readonly _getTiktokAccountPermissionsStatusUseCase: GetTiktokAccountPermissionsStatusUseCase,
        private readonly _getUbereatsAccountPermissionsStatusUseCase: GetUbereatsAccountPermissionsStatusUseCase,
        private readonly _getZenchefAccountPermissionsStatusUseCase: GetZenchefAccountPermissionsStatusUseCase,
        private readonly _getTripadvisorAccountPermissionsStatusUseCase: GetTripadvisorAccountPermissionsStatusUseCase,
        private readonly _getOpenTableAccountPermissionsStatusUseCase: GetOpenTableAccountPermissionsStatusUseCase
    ) {}

    async execute(platform: Platform): Promise<CredentialValidityStatus> {
        const getPermissionsStatusUseCase = {
            [PlatformKey.DELIVEROO]: this._getDeliverooAccountPermissionsStatusUseCase,
            [PlatformKey.FACEBOOK]: this._getFacebookAccountPermissionsStatusUseCase,
            [PlatformKey.GMB]: this._getGmbAccountPermissionsStatusUseCase,
            [PlatformKey.INSTAGRAM]: this._getFacebookAccountPermissionsStatusUseCase,
            [PlatformKey.TIKTOK]: this._getTiktokAccountPermissionsStatusUseCase,
            [PlatformKey.UBEREATS]: this._getUbereatsAccountPermissionsStatusUseCase,
            [PlatformKey.ZENCHEF]: this._getZenchefAccountPermissionsStatusUseCase,
            [PlatformKey.TRIPADVISOR]: this._getTripadvisorAccountPermissionsStatusUseCase,
            [PlatformKey.OPENTABLE]: this._getOpenTableAccountPermissionsStatusUseCase,
        }[platform.key];

        if (!getPermissionsStatusUseCase) {
            throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
                message: `PermissionsStatusUseCase is not implemented`,
                metadata: {
                    platformKey: platform.key,
                    platform,
                    restaurantId: platform.restaurantId,
                },
            });
        }

        return getPermissionsStatusUseCase.execute(platform);
    }
}
