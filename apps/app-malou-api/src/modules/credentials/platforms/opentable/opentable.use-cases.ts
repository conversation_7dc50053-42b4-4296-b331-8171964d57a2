import { singleton } from 'tsyringe';

import { OpenTableLightRestaurant } from ':providers/opentable/interfaces/opentable.restaurant.interface';
import { OpenTableProvider } from ':providers/opentable/opentable.provider';

@singleton()
export default class OpenTableCredentialsUseCases {
    constructor(private readonly _openTableProvider: OpenTableProvider) {}

    async getRestaurant({ socialId }): Promise<OpenTableLightRestaurant> {
        return await this._openTableProvider.getLightRestaurant({ socialId });
    }

    async getTodayRating({ socialId }): Promise<number> {
        return await this._openTableProvider.getTodayRating({ socialId });
    }
}
