import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { logger } from ':helpers/logger';
import { CredentialValidityStatus } from ':modules/credentials/platforms/interfaces';
import { Platform } from ':modules/platforms/platforms.entity';
import { OpenTableProvider } from ':providers/opentable/opentable.provider';

@singleton()
export class GetOpenTableAccountPermissionsStatusUseCase {
    constructor(private readonly _openTableProvider: OpenTableProvider) {}

    async execute(platform: Platform): Promise<CredentialValidityStatus> {
        assert(platform.socialId, 'Platform socialId is required to check OpenTable permissions status');
        try {
            const restaurant = await this._openTableProvider.getRestaurant({ socialId: platform.socialId });
            if (!restaurant) {
                return {
                    isValid: false,
                    missing: [],
                };
            }
            return {
                isValid: restaurant.core.restaurant.restaurantId.toString() === platform.socialId,
                missing: [],
            };
        } catch (error) {
            logger.info('[OPENTABLE_CREDENTIALS_ERROR] Error while checking permissions', error);

            return {
                isValid: true,
                missing: [],
                miscellaneous: 'An error occurred but the credentials are still valid',
            };
        }
    }
}
