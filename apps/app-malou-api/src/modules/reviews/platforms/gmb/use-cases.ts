import { pick } from 'lodash';
import assert from 'node:assert/strict';
import { autoInjectable, delay, inject } from 'tsyringe';

import { IPlatform, IReview, IReviewComment } from '@malou-io/package-models';
import { GmbNotificationType, MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { isRejected } from ':helpers/utils';
import { GmbApiProviderUseCases } from ':modules/credentials/platforms/gmb/gmb.use-cases';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { GmbMediaItemOutput } from ':modules/platforms/platforms/gmb/gmb.types';
import { GmbAttachments, GmbReplyPayload, GmbReviewWithAttachments } from ':modules/reviews/platforms/gmb/interface';
import { ReviewMapper } from ':modules/reviews/reviews.mapper';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { PlatformReview, ReviewInputWithRestaurantAndPlatformIds } from ':modules/reviews/reviews.types';

@autoInjectable()
export default class GmbReviewsUseCases {
    constructor(
        @inject(delay(() => PlatformsRepository)) private readonly platformsRepository: PlatformsRepository,
        @inject(delay(() => ReviewsRepository)) private readonly reviewsRepository: ReviewsRepository,
        @inject(delay(() => GmbApiProviderUseCases)) private readonly gmbApiProviderUseCases: GmbApiProviderUseCases
    ) {}

    getReviewsData = async (
        { restaurantId }: { restaurantId: string },
        recentOnly: boolean
    ): Promise<GmbReviewWithAttachments[] | undefined> => {
        const platform = await this.platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.GMB);
        assert(platform, 'Missing platform');
        const { credentials, apiEndpoint } = platform;
        const credentialId = credentials?.[0];
        if (!credentialId || !apiEndpoint) {
            logger.warn('[GMB_REVIEWS][GET_REVIEWS_DATA] No credential found', { restaurantId, platformId: platform?._id });
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND, { message: 'No credential found gmb' });
        }
        await this._checkNotificationSettings(credentialId, apiEndpoint);
        const [reviews, reviewsMedia] = await Promise.allSettled([
            this.gmbApiProviderUseCases.fetchAllLocationReviews(credentialId, apiEndpoint, recentOnly),
            this.gmbApiProviderUseCases.fetchAllLocationReviewsMedia(credentialId, apiEndpoint, recentOnly),
        ]);
        const reviewsMediaValue = isRejected(reviewsMedia) ? undefined : reviewsMedia.value;
        const reviewsValue = isRejected(reviews) ? undefined : reviews.value;
        if (!reviewsMediaValue?.length) {
            return reviewsValue;
        }
        assert(reviewsValue, 'Missing reviews');
        return this._linkMediaToReviews(reviewsValue, reviewsMediaValue);
    };

    mapReviewsDataToMalou = function (platform: IPlatform, reviewsData: PlatformReview[]): ReviewInputWithRestaurantAndPlatformIds[] {
        return reviewsData.map((review) => ReviewMapper.mapToMalouReview(platform, review));
    };

    pushReviewComment = ({ socialId, key, comment }: { socialId: string; key: string; comment: IReviewComment }): Promise<any> =>
        this.reviewsRepository.updateUniqueReviewComment({
            socialId,
            key,
            comment,
        });

    /**
     * For gmb, create and update have the same behaviour
     */
    updateComment = ({ review, comment }: { review: IReview; comment: { comment: string } }): Promise<any> =>
        this.reply({
            review,
            comment,
        });

    /**
     * Builds a map { [key: string]: array } ( profilePhotoId : socialAttachments[] )
     */
    buildMediaItemsMap = (mediaItems: Record<string, any>[]): Record<string, GmbMediaItemOutput[]> => {
        const mediaItemsMap: Record<string, GmbMediaItemOutput[]> = {};
        mediaItems.forEach((media) => {
            const profilePhotoId = this._getProfilePictureId(media.attribution.profilePhotoUrl);
            const socialAttachment = pick(media, ['name', 'mediaFormat', 'googleUrl', 'thumbnailUrl', 'createTime']);

            if (mediaItemsMap[profilePhotoId!]) {
                mediaItemsMap[profilePhotoId!].push(socialAttachment);
            } else {
                mediaItemsMap[profilePhotoId!] = [socialAttachment];
            }
        });
        return mediaItemsMap;
    };

    /**
     * Publish new review reply, and update malou database if successful
     */
    reply = async ({ review, comment }: { review: IReview; comment: GmbReplyPayload }): Promise<any> => {
        const platform = await this.platformsRepository.getPlatformByRestaurantIdAndPlatformKey(
            review.restaurantId.toString(),
            PlatformKey.GMB
        );

        assert(platform, 'Missing platform');
        const { credentials, apiEndpoint } = platform;
        const credentialId = credentials?.[0];
        if (!credentialId || !apiEndpoint) {
            logger.warn('[GMB_REVIEWS][REPLY] No credential found', { restaurantId: review.restaurantId });
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND, { message: 'No credential found gmb' });
        }
        const socialId = `${apiEndpoint}/reviews/${review.socialId}`;
        return this.gmbApiProviderUseCases.updateReviewReply(credentialId, socialId, comment);
    };

    async fetchTotalReviewCount(restaurantId: string): Promise<number> {
        const platform = await this.platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.GMB);

        if (!platform) {
            logger.warn('[GMB_REVIEWS][FETCH_TOTAL_REVIEWS_COUNT] No platform found', { restaurantId });

            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: {
                    restaurantId,
                    platformKey: PlatformKey.GMB,
                },
            });
        }
        const { credentials, apiEndpoint } = platform;
        const credentialId = credentials?.[0];
        if (!credentialId || !apiEndpoint) {
            logger.warn('[GMB_REVIEWS][FETCH_TOTAL_REVIEWS_COUNT] No credential found', { restaurantId });
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND, { message: 'No credential found gmb' });
        }
        const firstReviewsPage = await this.gmbApiProviderUseCases.fetchLocationReviews(credentialId, apiEndpoint);
        return firstReviewsPage?.totalReviewCount;
    }

    /**
     * Links each review to associated media
     */
    private _linkMediaToReviews(reviews: GmbReviewWithAttachments[], mediaItems: GmbAttachments[]): GmbReviewWithAttachments[] {
        const mediaItemsMap = this.buildMediaItemsMap(mediaItems);
        const mediaReviewersNameItemMap = this._buildMediaItemsBasedOnReviewersName(mediaItems);
        return reviews.map((review) => {
            const reviewerProfilPhotoId = this._getProfilePictureId(review.reviewer?.profilePhotoUrl);
            const reviewerNameAndDate = `${review.reviewer.displayName.replace(' ', '_')}_${review.updateTime?.split('T')[0]}`;
            if (mediaItemsMap[reviewerProfilPhotoId!]) {
                return {
                    ...review,
                    socialAttachments: mediaItemsMap[reviewerProfilPhotoId!],
                };
                // eslint-disable-next-line no-else-return
            } else if (mediaReviewersNameItemMap[reviewerNameAndDate]) {
                return {
                    ...review,
                    socialAttachments: mediaReviewersNameItemMap[reviewerNameAndDate],
                };
            }
            return review;
        });
    }

    /**
     * Extract id from google user photo url
     * https://lh3.googleusercontent.com/a/AATXAJweP_GVR6dFQDVaQRPAipOD-eN8l8QRyZICspI1=s120-c-c0x00000000-cc-rp-mo-br100 -> AATXAJweP_GVR6dFQDVaQRPAipOD-eN8l8QRyZICspI1
     */
    private _getProfilePictureId(profilePictureUrl: string) {
        if (!profilePictureUrl) {
            return null;
        }
        return profilePictureUrl.match(/[0-9a-zA-Z\-\_]{44}/)?.[0] || null;
    }

    /**
     * Builds a map { [key: string]: array } ( reviewerName : socialAttachments[] )
     * @param {Object[]} mediaItems
     */
    private _buildMediaItemsBasedOnReviewersName(mediaItems) {
        const mediaReviewersNameItemMap = {};
        mediaItems.forEach((media) => {
            const reviewerNameAndDate = `${media.attribution?.profileName?.replace(' ', '_')}_${media.createTime?.split('T')[0]}`;
            const socialAttachment = (({ mediaFormat, googleUrl, thumbnailUrl, createTime }) => ({
                mediaFormat,
                googleUrl,
                thumbnailUrl,
                createTime,
            }))(media);

            if (mediaReviewersNameItemMap[reviewerNameAndDate]) {
                mediaReviewersNameItemMap[reviewerNameAndDate].push(socialAttachment);
            } else {
                mediaReviewersNameItemMap[reviewerNameAndDate] = [socialAttachment];
            }
        });
        return mediaReviewersNameItemMap;
    }

    private async _checkNotificationSettings(credentialId: string, apiEndpoint: string) {
        try {
            const account = apiEndpoint.split('/')[1];
            const { notificationsTypes } = await this.gmbApiProviderUseCases.getNotificationsSettings(credentialId, account);
            if (notificationsTypes?.join(',') === Object.values(GmbNotificationType).join(',')) {
                return;
            }
            return this.gmbApiProviderUseCases.patchNotificationSettings(credentialId, account);
        } catch (error) {
            if (error instanceof MalouError && error.metadata?.err?.response?.status === 404) {
                logger.warn('[GMB_REVIEWS][CHECK_NOTIFICATION_SETTINGS] Gmb account not found', {
                    credentialId,
                    apiEndpoint,
                });
                throw new MalouError(MalouErrorCode.GMB_ACCOUNT_NOT_FOUND, {
                    message: "We can't access your Google account, you may need to deconnect and reconnect Google platform",
                });
            }

            logger.error('[GMB_REVIEWS][CHECK_NOTIFICATION_SETTINGS] Error while checking notification settings', {
                credentialId,
                apiEndpoint,
            });
            throw error;
        }
    }
}
