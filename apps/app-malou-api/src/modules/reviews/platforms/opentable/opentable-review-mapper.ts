import { <PERSON><PERSON><PERSON>, PlatformPresenceStatus, PostedStatus } from '@malou-io/package-utils';

import { ReviewCommentInput, ReviewInput, ReviewInputRating } from ':modules/reviews/reviews.types';
import { OpenTableReview } from ':providers/opentable/interfaces/opentable.reviews.interface';

export type OpentableReplyPayload = { comment: string };

export class OpentableReviewMapper {
    static mapToMalouReview(review: OpenTableReview): ReviewInput {
        return {
            key: PlatformKey.OPENTABLE,
            socialId: review.id,
            businessSocialLink: `https://guestcenter.opentable.com/restaurant/${review.restaurantId}/feedback/reviews/collection?displayMetadata=false&reviewId=${review.id}`,
            text: review.comment || null,
            socialCreatedAt: review.reviewDate,
            rating: review.overallRating as ReviewInputRating,
            reviewer: review.guestName
                ? {
                      socialId: undefined,
                      profilePhotoUrl: undefined,
                      displayName: review.guestName,
                      socialUrl: undefined,
                  }
                : undefined,
            comments: review.responses?.length ? review.responses.map(OpentableReviewMapper.mapToMalouReply) : [],
            socialAttachments: [],
            title: undefined,
            socialLink: null,
            socialRating: review.overallRating,
            lang: null,
            socialUpdatedAt: null,
            platformPresenceStatus: PlatformPresenceStatus.FOUND,
        };
    }

    static mapToPlatformReply(replyText: string): OpentableReplyPayload {
        return {
            comment: replyText,
        };
    }

    static mapToMalouReply(reviewReply: OpenTableReview['responses'][number] | OpentableReplyPayload): ReviewCommentInput {
        if (OpentableReviewMapper._isReplyPayload(reviewReply)) {
            return {
                socialId: undefined,
                text: reviewReply.comment,
                posted: PostedStatus.PENDING,
                socialUpdatedAt: undefined,
                user: undefined,
            };
        }

        return {
            socialId: reviewReply.id?.toString() ?? reviewReply.externalResponseId ?? null,
            text: reviewReply.content,
            posted: reviewReply.isPublished ? PostedStatus.POSTED : PostedStatus.PENDING,
            socialUpdatedAt: reviewReply.createdAt ?? null,
            user: undefined,
        };
    }

    private static _isReplyPayload(reply: OpenTableReview['responses'][number] | OpentableReplyPayload): reply is OpentableReplyPayload {
        return (reply as OpentableReplyPayload).comment !== undefined;
    }
}
