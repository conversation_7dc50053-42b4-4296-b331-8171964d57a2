import assert from 'node:assert/strict';
import { autoInjectable, delay, inject } from 'tsyringe';

import { IPlatform } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { ReviewMapper } from ':modules/reviews/reviews.mapper';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { PlatformReviewsUseCases, ReviewInputWithRestaurantAndPlatformIds } from ':modules/reviews/reviews.types';
import { OpenTableReview } from ':providers/opentable/interfaces/opentable.reviews.interface';
import { OpenTableProvider } from ':providers/opentable/opentable.provider';

@autoInjectable()
export default class OpentableReviewsUseCases implements PlatformReviewsUseCases {
    constructor(
        @inject(delay(() => ReviewsRepository)) private readonly reviewsRepository: ReviewsRepository,
        private readonly _openTableProvider: OpenTableProvider
    ) {}

    async getReviewsData({ socialId }: { socialId?: string }) {
        assert(socialId, 'Missing socialId');
        return this._openTableProvider.getAllReviews({ socialId });
    }

    mapReviewsDataToMalou(platform: IPlatform, reviewsData: OpenTableReview[]): ReviewInputWithRestaurantAndPlatformIds[] {
        return reviewsData.map((review) => ReviewMapper.mapToMalouReview(platform, review));
    }

    reply({ comment }) {
        return comment;
    }

    pushReviewComment = ({ socialId, key, comment }) => this.reviewsRepository.pushReviewComment({ socialId, key, comment });

    updateComment = async function () {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            metadata: {
                platform: PlatformKey.OPENTABLE,
            },
        });
    };

    async fetchTotalReviewCount(_restaurantId: string): Promise<number> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'OpentableReviewsUseCases does not implement fetchTotalReviewCount',
        });
    }
}
