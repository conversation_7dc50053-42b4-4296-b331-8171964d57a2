import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { ReviewWithTranslationsResponseDto } from '@malou-io/package-dto';
import { toDbId } from '@malou-io/package-models';

import { ReviewsDtoMapper } from ':modules/reviews/reviews.mapper.dto';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { fakeReviewsRelatedToCommentOption } from ':modules/reviews/use-cases/get-review-related-to-comment-option/fake-reviews-related-to-comment-option';

@singleton()
export class GetReviewRelatedToCommentOptionUseCase {
    constructor(
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _reviewsDtoMapper: ReviewsDtoMapper
    ) {}

    async execute(restaurantId: string, rating: number): Promise<ReviewWithTranslationsResponseDto | null> {
        const reviewId = await this._getReviewIdRelatedToCommentOption(restaurantId, rating);
        if (!reviewId) {
            return this._getFakeReviewWithTranslationsResponseDto(rating);
        }
        const reviewWithTranslations = await this._reviewsRepository.getReviewByIdWithTranslation(reviewId);
        assert(reviewWithTranslations, 'Review with translations not found');
        return this._reviewsDtoMapper.toReviewWithTranslationsResponseDto(reviewWithTranslations);
    }

    private async _getReviewIdRelatedToCommentOption(restaurantId: string, rating: number): Promise<string | null> {
        const review = await this._reviewsRepository.findOne({
            filter: { restaurantId: toDbId(restaurantId), rating, text: { $ne: null }, reviewerNameValidation: { $ne: null } },
            projection: { _id: 1 },
            options: { lean: true, sort: { socialSortDate: -1 } },
        });
        if (review) {
            return review._id.toString();
        }

        const validReviewerNameRegex = /^[a-zA-Z]*( [a-zA-Z]*)+$/; // Matches names with at least two words and no numbers or special characters
        const reviewWithoutReviewerNameValidationButAValidReviewerName = await this._reviewsRepository.findOne({
            filter: {
                restaurantId: toDbId(restaurantId),
                rating,
                text: { $ne: null },
                reviewerNameValidation: null,
                'reviewer.displayName': { $regex: validReviewerNameRegex },
            },
            projection: { _id: 1 },
            options: { lean: true },
        });
        if (reviewWithoutReviewerNameValidationButAValidReviewerName) {
            return reviewWithoutReviewerNameValidationButAValidReviewerName._id.toString();
        }
        return null;
    }

    private _getFakeReviewWithTranslationsResponseDto(rating: number): ReviewWithTranslationsResponseDto {
        const fakeReview = fakeReviewsRelatedToCommentOption[rating.toString()];
        assert(fakeReview, `No fake review found for rating ${rating}`);
        return fakeReview;
    }
}
