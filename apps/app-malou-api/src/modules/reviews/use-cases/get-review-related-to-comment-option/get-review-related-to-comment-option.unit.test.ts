import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { Civility } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { DEFAULT_REVIEWER_NAME_VALIDATION } from ':microservices/ai-previous-review-analysis.service';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { GetReviewRelatedToCommentOptionUseCase } from ':modules/reviews/use-cases/get-review-related-to-comment-option/get-review-related-to-comment-option.use-case';

let getReviewRelatedToCommentOptionUseCase: GetReviewRelatedToCommentOptionUseCase;

describe('GetReviewRelatedToCommentOptionUseCase', () => {
    beforeAll(() => {
        container.clearInstances();

        registerRepositories(['ReviewsRepository']);

        getReviewRelatedToCommentOptionUseCase = container.resolve(GetReviewRelatedToCommentOptionUseCase);
    });

    describe('execute', () => {
        describe('Given no reviews for restaurant', () => {
            it('should return fake review', async () => {
                const restaurantId = newDbId();
                const rating = 5;

                const result = await getReviewRelatedToCommentOptionUseCase.execute(restaurantId.toString(), rating);

                expect(result).toEqual(
                    expect.objectContaining({
                        id: '5',
                        rating,
                    })
                );
            });
        });
        describe('Given a review with rating', () => {
            it('should return fake review if it doesnt match the rating', async () => {
                const restaurantId = newDbId();
                const rating = 5;
                const testCase = new TestCaseBuilderV2<'reviews'>({
                    seeds: {
                        reviews: {
                            data() {
                                return [getDefaultReview().restaurantId(restaurantId).rating(1).build()];
                            },
                        },
                    },
                    expectedResult(): any {
                        return {
                            id: '5',
                            rating,
                        };
                    },
                });
                await testCase.build();

                const result = await getReviewRelatedToCommentOptionUseCase.execute(restaurantId.toString(), rating);

                const expectedResult = testCase.getExpectedResult();
                expect(result).toEqual(expect.objectContaining(expectedResult));
            });

            it('should return fake review if it matches the rating but has no text', async () => {
                const restaurantId = newDbId();
                const rating = 5;
                const testCase = new TestCaseBuilderV2<'reviews'>({
                    seeds: {
                        reviews: {
                            data() {
                                return [getDefaultReview().restaurantId(restaurantId).rating(rating).text(null).build()];
                            },
                        },
                    },
                    expectedResult(): any {
                        return {
                            id: '5',
                            rating,
                        };
                    },
                });
                await testCase.build();

                const result = await getReviewRelatedToCommentOptionUseCase.execute(restaurantId.toString(), rating);

                const expectedResult = testCase.getExpectedResult();
                expect(result).toEqual(expect.objectContaining(expectedResult));
            });
        });

        describe('Given a review with rating and text', () => {
            describe('and no reviewer name validation', () => {
                it('should return fake review if reviewer name has number in it', async () => {
                    const restaurantId = newDbId();
                    const rating = 4;
                    const testCase = new TestCaseBuilderV2<'reviews'>({
                        seeds: {
                            reviews: {
                                data() {
                                    return [
                                        getDefaultReview()
                                            .restaurantId(restaurantId)
                                            .rating(rating)
                                            .text('Great review!')
                                            .reviewerNameValidation(null)
                                            .reviewer({
                                                displayName: 'TheGreatReviewer123',
                                            })
                                            .build(),
                                    ];
                                },
                            },
                        },
                        expectedResult(): any {
                            return {
                                id: '4',
                                rating,
                            };
                        },
                    });
                    await testCase.build();

                    const result = await getReviewRelatedToCommentOptionUseCase.execute(restaurantId.toString(), rating);

                    const expectedResult = testCase.getExpectedResult();
                    expect(result).toEqual(expect.objectContaining(expectedResult));
                });

                it('should return fake review if reviewer name has no space in it', async () => {
                    const restaurantId = newDbId();
                    const rating = 3;
                    const testCase = new TestCaseBuilderV2<'reviews'>({
                        seeds: {
                            reviews: {
                                data() {
                                    return [
                                        getDefaultReview()
                                            .restaurantId(restaurantId)
                                            .rating(rating)
                                            .text('Great review!')
                                            .reviewerNameValidation(null)
                                            .reviewer({
                                                displayName: 'TheGreatReviewer',
                                            })
                                            .build(),
                                    ];
                                },
                            },
                        },
                        expectedResult(): any {
                            return {
                                id: '3',
                                rating,
                            };
                        },
                    });
                    await testCase.build();

                    const result = await getReviewRelatedToCommentOptionUseCase.execute(restaurantId.toString(), rating);

                    const expectedResult = testCase.getExpectedResult();
                    expect(result).toEqual(expect.objectContaining(expectedResult));
                });

                it('should return a review with valid reviewer name (no numbers and at least 2 words)', async () => {
                    const restaurantId = newDbId();
                    const rating = 5;
                    const testCase = new TestCaseBuilderV2<'reviews'>({
                        seeds: {
                            reviews: {
                                data() {
                                    return [
                                        getDefaultReview()
                                            .restaurantId(restaurantId)
                                            .rating(rating)
                                            .text('Great review!')
                                            .reviewerNameValidation(null)
                                            .reviewer({
                                                displayName: 'John Doe',
                                            })
                                            .build(),
                                    ];
                                },
                            },
                        },
                        expectedResult(): any {
                            return {
                                rating,
                                text: 'Great review!',
                                reviewerNameValidation: undefined,
                                reviewer: {
                                    displayName: 'John Doe',
                                },
                            };
                        },
                    });
                    await testCase.build();

                    const result = await getReviewRelatedToCommentOptionUseCase.execute(restaurantId.toString(), rating);

                    const expectedResult = testCase.getExpectedResult();
                    expect(result).toEqual(expect.objectContaining(expectedResult));
                });
            });

            describe('and reviewer name validation', () => {
                it('should return a review', async () => {
                    const restaurantId = newDbId();
                    const rating = 5;
                    const testCase = new TestCaseBuilderV2<'reviews'>({
                        seeds: {
                            reviews: {
                                data() {
                                    return [
                                        getDefaultReview()
                                            .restaurantId(restaurantId)
                                            .rating(rating)
                                            .text('Great review!')
                                            .reviewerNameValidation(DEFAULT_REVIEWER_NAME_VALIDATION)
                                            .build(),
                                    ];
                                },
                            },
                        },
                        expectedResult(): any {
                            return {
                                rating,
                                text: 'Great review!',
                                reviewerNameValidation: {
                                    gender: Civility.OTHER,
                                    firstName: '',
                                    isFirstNameValid: false,
                                    lastName: '',
                                    isLastNameValid: false,
                                },
                            };
                        },
                    });
                    await testCase.build();

                    const result = await getReviewRelatedToCommentOptionUseCase.execute(restaurantId.toString(), rating);

                    const expectedResult = testCase.getExpectedResult();
                    expect(result).toEqual(expect.objectContaining(expectedResult));
                });
            });
        });
    });
});
