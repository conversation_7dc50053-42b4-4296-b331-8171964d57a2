import { isNil } from 'lodash';
import { SQSMessage } from 'sqs-consumer';
import { singleton } from 'tsyringe';

import { MalouErrorCode } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { CreateNewReviewsNotificationProducer } from ':modules/notifications/queues/create-new-reviews-notification/create-new-reviews-notification.producer';
import { NotificationReviewsRepository } from ':modules/notifications/repositories/notifications-reviews.repository';
import ReviewsUseCases from ':modules/reviews/reviews.use-cases';
import { UseCaseQueueTag } from ':queues/sqs-template/constant';
import { GenericSqsConsumer } from ':queues/sqs-template/generic-sqs-consumer';

@singleton()
export class ReviewsConsumer extends GenericSqsConsumer {
    constructor(
        private readonly _reviewsUseCases: ReviewsUseCases,
        private readonly _reviewsRepository: NotificationReviewsRepository,
        private readonly _createNewReviewsNotificationProducer: CreateNewReviewsNotificationProducer
    ) {
        super({ useCaseQueueTag: UseCaseQueueTag.REVIEWS, queueUrl: Config.services.sqs.reviewsQueueUrl });
    }

    async handleMessage(msg: SQSMessage): Promise<void> {
        const jobStartDate = new Date();
        const body = msg.Body ? JSON.parse(msg.Body) : null;
        if (isNil(body)) {
            throw new MalouError(MalouErrorCode.SQS_MESSAGE_NOT_FOUND, { message: 'body is nil' });
        }
        const { platformId, recentOnly } = body;

        if (!platformId) {
            throw new MalouError(MalouErrorCode.SQS_INVALID_MESSAGE, { message: 'platformId not found' });
        }

        logger.info('Starting update restaurant reviews', { platformId, recentOnly });
        await this._reviewsUseCases.updateReviewsForPlatform(platformId, recentOnly);
        const newlyCreatedReviews = await this._reviewsRepository.getLatestCreatedReviewsForPlatform({
            platformId,
            since: jobStartDate,
        });

        if (newlyCreatedReviews.length === 0) {
            logger.info('No new reviews created');
            return;
        }

        if (newlyCreatedReviews.length > 0) {
            await this._createNewReviewsNotificationProducer.execute({
                reviewIds: newlyCreatedReviews.map((review) => review._id.toString()),
            });
        }
    }
}
