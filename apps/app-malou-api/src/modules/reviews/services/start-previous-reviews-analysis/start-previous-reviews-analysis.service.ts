import { singleton } from 'tsyringe';

import { IPrivateReview, IReview } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import { PreviousReviewsAnalysisProducer } from ':modules/reviews/queues/previous-reviews-analysis/previous-reviews-analysis.producer';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';

@singleton()
export class StartPreviousReviewsAnalysisService {
    constructor(
        private readonly _previousReviewsAnalysisProducer: PreviousReviewsAnalysisProducer,
        private readonly _reviewsRepository: ReviewsRepository
    ) {}

    async execute({ review }: { review: IReview | IPrivateReview }): Promise<void> {
        try {
            if (review.responseStyle || review.reviewerNameValidation) {
                logger.info('[StartPreviousReviewsAnalysisService] Review already has previous reviews analysis', {
                    reviewId: review._id,
                    semanticAnalysisFetchStatus: review.semanticAnalysisFetchStatus,
                });
                return;
            }

            await this._previousReviewsAnalysisProducer.execute({
                reviewId: review._id.toString(),
                restaurantId: review.restaurantId.toString(),
                lang: review.lang ?? undefined,
            });
        } catch (err) {
            logger.error('[StartPreviousReviewsAnalysisService] Error while starting previous reviews analysis', {
                reviewId: review._id,
                error: err,
            });
        }
    }
}
