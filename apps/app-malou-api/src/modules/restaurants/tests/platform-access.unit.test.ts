import { container } from 'tsyringe';

import { DbId, toDbId } from '@malou-io/package-models';
import { PlatformAccessStatus, PlatformAccessType, PlatformKey, Role } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { TheForkService } from ':modules/clients/provider-clients/providers/thefork/service/thefork.service';
import MailingUseCases from ':modules/mailing/use-cases';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultUser } from ':modules/users/tests/user.builder';

import { Access } from '../entities/access.entity';
import RestaurantsRepository from '../restaurants.repository';
import { CreatePlatformAccessUseCase } from '../use-cases/create-platform-access.use-case';
import { UpdatePlatformAccessStatusUseCase } from '../use-cases/update-platform-access-status.use-case';

describe('CreatePlatformAccessUseCase', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'UsersRepository']);
    });

    describe('execute', () => {
        const TheForkServiceMock = {
            requestRestaurantTheForkUuids: jest.fn(),
        } as unknown as TheForkService;
        container.register(TheForkService, { useValue: TheForkServiceMock });

        class MailingUseCasesMock {
            async sendEmail() {
                return;
            }
        }
        container.register(MailingUseCases, { useClass: MailingUseCasesMock as any });
        const restaurantRepository = container.resolve(RestaurantsRepository);
        const createPlatformAccessUseCase = container.resolve(CreatePlatformAccessUseCase);
        it('should create platform access status', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'users'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant()
                                    .access([
                                        {
                                            platformKey: PlatformKey.DELIVEROO,
                                            status: PlatformAccessStatus.VERIFIED,
                                            accessType: PlatformAccessType.AUTO,
                                            active: true,
                                            lastUpdated: new Date(),
                                            lastVerified: new Date(),
                                        },
                                        {
                                            platformKey: PlatformKey.ZENCHEF,
                                            status: PlatformAccessStatus.VERIFIED,
                                            accessType: PlatformAccessType.AUTO,
                                            active: true,
                                            lastUpdated: new Date(),
                                            lastVerified: new Date(),
                                        },
                                        {
                                            platformKey: PlatformKey.UBEREATS,
                                            status: PlatformAccessStatus.VERIFIED,
                                            accessType: PlatformAccessType.AUTO,
                                            active: true,
                                            lastUpdated: new Date(),
                                            lastVerified: new Date(),
                                        },
                                    ])
                                    .build(),
                            ];
                        },
                    },
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                },
                expectedResult: undefined,
            });
            await testCase.build();

            const seeds = testCase.getSeededObjects();

            const restaurantId = (seeds.restaurants[0]._id as DbId).toString();
            const userId = (seeds.users[0]._id as DbId).toString();

            await createPlatformAccessUseCase.execute(restaurantId, {
                platformAccess: new Access({
                    accessType: PlatformAccessType.CREDENTIALS,
                    active: true,
                    platformKey: PlatformKey.LAFOURCHETTE,
                    status: PlatformAccessStatus.NEED_REVIEW,
                    data: {
                        login: 'test',
                        password: 'willBeEncrypted',
                    },
                    lastUpdated: new Date(),
                    lastVerified: new Date(),
                }),
                user: {
                    _id: toDbId(userId),
                    role: Role.MALOU_BASIC,
                },
            });

            const restaurant = await restaurantRepository.findOne({
                filter: { _id: restaurantId },
                projection: { access: 1 },
                options: { lean: true },
            });

            const createdAccess = restaurant?.access.find((a) => a.platformKey === PlatformKey.LAFOURCHETTE);

            expect(createdAccess).toMatchObject({
                platformKey: PlatformKey.LAFOURCHETTE,
                status: PlatformAccessStatus.NEED_REVIEW,
                accessType: PlatformAccessType.CREDENTIALS,
                active: true,
                data: {
                    login: 'test',
                },
            });

            expect(createdAccess?.data?.password).not.toBe('willBeEncrypted');
        });
    });
});

describe('UpdatePlatformAccessStatusUseCase', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository']);
    });

    describe('execute', () => {
        const restaurantRepository = container.resolve(RestaurantsRepository);
        const updatePlatformAccessStatusUseCase = container.resolve(UpdatePlatformAccessStatusUseCase);
        it('should update platform access status', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant()
                                    .access([
                                        {
                                            platformKey: PlatformKey.DELIVEROO,
                                            status: PlatformAccessStatus.VERIFIED,
                                            accessType: PlatformAccessType.AUTO,
                                            active: true,
                                            lastUpdated: new Date(),
                                            lastVerified: new Date(),
                                        },
                                        {
                                            platformKey: PlatformKey.YELP,
                                            status: PlatformAccessStatus.NEED_REVIEW,
                                            accessType: PlatformAccessType.CREDENTIALS,
                                            active: true,
                                            data: {
                                                login: 'test',
                                                password: 'test',
                                            },
                                            lastUpdated: new Date(),
                                            lastVerified: new Date(),
                                        },
                                        {
                                            platformKey: PlatformKey.ZENCHEF,
                                            status: PlatformAccessStatus.VERIFIED,
                                            accessType: PlatformAccessType.AUTO,
                                            active: true,
                                            lastUpdated: new Date(),
                                            lastVerified: new Date(),
                                        },
                                        {
                                            platformKey: PlatformKey.UBEREATS,
                                            status: PlatformAccessStatus.VERIFIED,
                                            accessType: PlatformAccessType.AUTO,
                                            active: true,
                                            lastUpdated: new Date(),
                                            lastVerified: new Date(),
                                        },
                                    ])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: undefined,
            });
            await testCase.build();

            const seeds = testCase.getSeededObjects();

            const restaurantId = (seeds.restaurants[0]._id as DbId).toString();
            const access = seeds.restaurants[0].access;

            await updatePlatformAccessStatusUseCase.execute({
                restaurantId: restaurantId,
                accessType: PlatformAccessType.CREDENTIALS,
                active: true,
                platformKey: PlatformKey.YELP,
                status: PlatformAccessStatus.VERIFIED,
            });

            const restaurant = await restaurantRepository.findOne({
                filter: { _id: restaurantId },
                projection: { access: 1 },
                options: { lean: true },
            });

            expect(restaurant?.access.length).toBe(access.length);

            const updatedAccess = restaurant?.access.find((a) => a.platformKey === PlatformKey.YELP);

            expect(updatedAccess).toMatchObject({
                platformKey: PlatformKey.YELP,
                status: PlatformAccessStatus.VERIFIED,
                accessType: PlatformAccessType.CREDENTIALS,
                active: true,
                data: {
                    login: 'test',
                    password: 'test',
                },
            });
        });
    });
});
