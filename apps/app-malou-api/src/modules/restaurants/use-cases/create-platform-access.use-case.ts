import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { DbId, IRestaurant } from '@malou-io/package-models';
import {
    EmailCategory,
    EmailType,
    failedAccessStatus,
    InformationUpdatePlatformStateStatus,
    InformationUpdateProvider,
    MalouErrorCode,
    PlatformAccessType,
    PlatformKey,
    platformKeyToSupportedPlatformKey,
    Role,
} from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { encryptPassword } from ':helpers/utils';
import { TheForkService } from ':modules/clients/provider-clients/providers/thefork/service/thefork.service';
import { UpdatePlatformStateStatusService } from ':modules/information-updates/services/update-platform-state-status.service';
import MailingUseCases from ':modules/mailing/use-cases';
import YextListingService from ':modules/publishers/yext/services/yext-listing.service';
import { Access } from ':modules/restaurants/entities/access.entity';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class CreatePlatformAccessUseCase {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _mailingUseCases: MailingUseCases,
        private readonly _updatePlatformStateStatusService: UpdatePlatformStateStatusService,
        private readonly _yextListingService: YextListingService,
        private readonly _theForkService: TheForkService
    ) {}

    async execute(
        restaurantId: string,
        { platformAccess, user }: { platformAccess: Access; user: { _id: DbId; role: Role } }
    ): Promise<IRestaurant> {
        const userId = user._id.toString();
        logger.info('[UPDATE_RESTAURANT_ACCESS] Updating platform access', { restaurantId, userId, platformAccess });
        const restaurant = await this._restaurantsRepository.getRestaurantById(restaurantId);
        if (!restaurant) {
            throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, {
                message: 'Cannot update restaurant organization : restaurant not found',
                metadata: {
                    restaurantId,
                },
            });
        }

        const currentPlatformAccesses = (restaurant.access ?? []).filter((a) => a.platformKey === platformAccess.platformKey);
        const newPlatformAccesses = this._getNewPlatformAccesses(currentPlatformAccesses, platformAccess);
        const restaurantAccess = restaurant.access
            .filter((ra) => ra.platformKey !== platformAccess.platformKey)
            .concat(newPlatformAccesses);
        const updatedRestaurant = await this._restaurantsRepository.updateRestaurantAccess(restaurantId, restaurantAccess);
        assert(updatedRestaurant, 'Did not find restaurant');

        if (this._doesAMailNeedToBeSentToAdmin(platformAccess, user)) {
            this._sendAdminNotificationEmail(restaurantId, userId).catch((error) =>
                logger.error('[UPDATE_RESTAURANT_ACCESS] Error sending email to admin', { restaurantId, userId, error })
            );
        }
        if (this._doesAMailNeedToBeSentToUser(platformAccess)) {
            this._sendUserNotificationEmail(restaurantId, platformAccess.platformKey, userId).catch((error) =>
                logger.error('[UPDATE_RESTAURANT_ACCESS] Error sending email to user', { restaurantId, userId, error })
            );
        }

        logger.info('[UPDATE_RESTAURANT_ACCESS] Success', { restaurantId, userId, updatedRestaurant });

        const isPlatformKeyHandledByYext = await this._yextListingService.isPlatformKeyHandledByYext({
            platformKey: platformAccess.platformKey,
            restaurantId,
        });
        const platformKey = platformKeyToSupportedPlatformKey[platformAccess.platformKey];
        if (!isPlatformKeyHandledByYext && platformKey) {
            await this._updatePlatformStateStatusService.updateInformationUpdatePlatformStateStatus({
                platformKey,
                provider: InformationUpdateProvider.MALOU,
                restaurantId,
                status: InformationUpdatePlatformStateStatus.PENDING,
            });
        }

        // Side-effect: for TheFork new access, we need to send a request to TheFork to connect the restaurant to their Clients API
        if (platformAccess.platformKey === PlatformKey.LAFOURCHETTE) {
            await this._theForkService.requestRestaurantTheForkUuids(restaurantId);
        }

        return updatedRestaurant;
    }

    private _getNewPlatformAccesses = (currentPlatformAccesses, platformAccess) => {
        const access = {
            ...(currentPlatformAccesses.find((a) => a.accessType === platformAccess.accessType) ?? {}),
            ...platformAccess,
        };
        let newAccesses = currentPlatformAccesses;
        if (platformAccess.lastUpdated) {
            // if it's an update we have to set other access to inactive
            newAccesses = newAccesses.map((a) => ({
                ...a,
                active: false,
            }));
            if (platformAccess.accessType === PlatformAccessType.CREDENTIALS && platformAccess.data && platformAccess.data.password) {
                access.data.password = encryptPassword(access.data.password, Config.cryptoJs.secret);
            }
        }
        switch (platformAccess.accessType) {
            case PlatformAccessType.CREDENTIALS:
            case PlatformAccessType.MANAGER:
            case PlatformAccessType.AUTO:
                newAccesses = newAccesses.filter((a) => a.accessType !== platformAccess.accessType).concat([access]);
                break;
            case PlatformAccessType.NO_UPDATE:
                break;
            default:
                throw new MalouError(MalouErrorCode.INVALID_DATA, {
                    metadata: { platformAccess },
                });
        }
        return newAccesses;
    };

    private _doesAMailNeedToBeSentToAdmin = (platformAccess: Access, user: { role: Role }) => {
        return !failedAccessStatus.includes(platformAccess.status) && user.role !== Role.ADMIN;
    };

    private _sendAdminNotificationEmail = async (restaurantId: string, userId: string) => {
        logger.info('[UPDATE_RESTAURANT_ACCESS] Sending email to admin', { restaurantId, userId });
        this._mailingUseCases
            .sendEmail(EmailCategory.ADMIN_NOTIF, EmailType.ACCESS_UPDATE, { restaurantId, userId })
            .catch((error) => logger.error('[UPDATE_RESTAURANT_ACCESS] Error sending email to admin', { restaurantId, userId, error }));
    };

    private _doesAMailNeedToBeSentToUser = (platformAccess: Access) => {
        return failedAccessStatus.includes(platformAccess.status);
    };

    private _sendUserNotificationEmail = async (restaurantId: string, platformKey: PlatformKey, userId: string) => {
        logger.info('[UPDATE_RESTAURANT_ACCESS] Sending email to user', { restaurantId, userId });
        this._mailingUseCases
            .sendEmail(EmailCategory.USER_NOTIF, EmailType.WRONG_PLATFORM_ACCESS, {
                restaurantId,
                platformKey,
            })
            .catch((error) => logger.warn('[UPDATE_RESTAURANT_ACCESS] Error sending email to user', { restaurantId, userId, error }));
    };
}
