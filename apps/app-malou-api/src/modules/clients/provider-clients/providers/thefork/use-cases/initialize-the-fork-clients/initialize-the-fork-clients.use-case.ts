import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import ProviderClientsRepository from ':modules/clients/provider-clients/provider-clients.repository';
import { TheForkService } from ':modules/clients/provider-clients/providers/thefork/service/thefork.service';
import { TheForkGSheetHeader } from ':modules/clients/provider-clients/providers/thefork/service/thefork.service.interface';
import { FetchTheForkClientListUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client-list/providers/fetch-thefork-client-list.use-case';
import { FetchTheForkVisitListUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-visit-list/providers/fetch-thefork-visit-list.use-case';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class InitializeTheForkClientsUseCase {
    constructor(
        private readonly _providerClientsRepository: ProviderClientsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _fetchTheForkClientListUseCase: FetchTheForkClientListUseCase,
        private readonly _fetchTheForkVisitListUseCase: FetchTheForkVisitListUseCase,
        private readonly _theForkService: TheForkService
    ) {}

    async execute(): Promise<void> {
        logger.info('[InitializeTheForkClientsUseCase] Starting');
        const sheet = await this._theForkService.getGoogleSpreadSheet();
        const rows = await sheet.getRows();
        const rowsNotInitialized = rows.filter(
            (row) =>
                row[TheForkGSheetHeader.MALOU_ID] &&
                row[TheForkGSheetHeader.RESTAURANT_UUID] &&
                row[TheForkGSheetHeader.GROUP_UUID] &&
                row[TheForkGSheetHeader.INITIALIZATION_MALOU] === 'FALSE'
        );

        logger.info('[InitializeTheForkClientsUseCase] Found ' + rowsNotInitialized.length + ' rows to initialize');

        for (const row of rowsNotInitialized) {
            const restaurantId = row[TheForkGSheetHeader.MALOU_ID];
            const restaurant = await this._restaurantsRepository.findOne({
                filter: { _id: toDbId(restaurantId) },
                projection: { openingDate: 1, createdAt: 1 },
                options: { lean: true },
            });

            if (!restaurant) {
                logger.error('[InitializeTheForkClientsUseCase] No restaurant found for', { restaurantId });
                continue;
            }

            const endDate = new Date();
            const startDate = restaurant.openingDate ?? restaurant.createdAt;

            const clients = await this._fetchTheForkClientListUseCase.execute(startDate, endDate, restaurantId);
            const reservations = await this._fetchTheForkVisitListUseCase.execute(startDate, endDate, restaurantId);

            const clientsWithVisits = clients.map((client) => {
                const clientReservations = reservations.filter(
                    (reservation) => reservation.providerVisitFields.customerUuid === client.providerClientId
                );
                client.visits = clientReservations;
                return client;
            });

            logger.info('[InitializeTheForkClientsUseCase] Upserting clients', { clientsCount: clientsWithVisits.length });
            await this._providerClientsRepository.upsertManyProviderClients(clientsWithVisits);

            row[TheForkGSheetHeader.INITIALIZATION_MALOU] = 'TRUE';
        }

        await sheet.saveUpdatedCells();
        logger.info('[InitializeTheForkClientsUseCase] Done');
    }
}
