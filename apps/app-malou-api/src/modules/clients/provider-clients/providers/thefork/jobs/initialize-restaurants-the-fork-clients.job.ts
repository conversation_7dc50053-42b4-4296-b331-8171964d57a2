import { singleton } from 'tsyringe';

import { GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { Config } from ':config';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';
import { InitializeTheForkClientsUseCase } from ':modules/clients/provider-clients/providers/thefork/use-cases/initialize-the-fork-clients/initialize-the-fork-clients.use-case';

@singleton()
export class InitializeRestaurantsTheForkClientsJob extends GenericJobDefinition {
    constructor(private readonly _initializeTheForkClientsUseCase: InitializeTheForkClientsUseCase) {
        super({
            agendaJobName: AgendaJobName.INITIALIZE_RESTAURANTS_THE_FORK_CLIENTS,
            shouldDeleteJobOnSuccess: true,
        });
    }

    async executeJob(): Promise<void> {
        if (Config.env !== 'production') {
            logger.warn('Disabled in non-production environments');
            return;
        }
        await this._initializeTheForkClientsUseCase.execute();
    }
}
