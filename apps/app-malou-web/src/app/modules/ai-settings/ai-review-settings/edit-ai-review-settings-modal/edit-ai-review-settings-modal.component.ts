import { Async<PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, OnInit, signal, ViewChild, WritableSignal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormArray, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { filter, map, tap } from 'rxjs';

import {
    ApplicationLanguage,
    CommentOptionValue,
    CustomerNaming,
    FrenchTutoiementVouvoiement,
    isObjectRequired,
    KeywordScoreTextType,
} from '@malou-io/package-utils';

import { MalouSpinnerComponent } from ':core/components/spinner/spinner/malou-spinner.component';
import { AiService } from ':core/services/ai.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { ScreenSizeService } from ':core/services/screen-size.service';
import { ToastService } from ':core/services/toast.service';
import { AiAdvancedReviewSettingsModalTabComponent } from ':modules/ai-settings/ai-review-settings/edit-ai-review-settings-modal/ai-advanced-review-settings-modal-tab/ai-advanced-review-settings-modal-tab.component';
import { AiGeneralReviewSettingsModalTabComponent } from ':modules/ai-settings/ai-review-settings/edit-ai-review-settings-modal/ai-general-review-settings-modal-tab/ai-general-review-settings-modal-tab.component';
import { AiSettingsContext } from ':modules/ai-settings/ai-settings.context';
import { ReviewsService } from ':modules/reviews/reviews.service';
import { CloseWithoutSavingModalComponent } from ':shared/components/close-without-saving-modal/close-without-saving-modal.component';
import { KeywordsScoreGaugeComponent } from ':shared/components/keywords-score-gauge/keywords-score-gauge.component';
import { Indication, KeywordsScoreGaugeType } from ':shared/components/keywords-score-gauge/keywords-score-gauge.interface';
import { SelectLanguagesComponent, SelectLanguagesDisplayStyle } from ':shared/components/select-languages/select-languages.component';
import { SelectComponent } from ':shared/components/select/select.component';
import { StarGaugeComponent } from ':shared/components/star-gauge/star-gauge.component';
import { formatDate } from ':shared/helpers';
import { DEFAULT_REVIEWER_NAME_VALIDATION, highlightKeywordsInText, Keyword, Review } from ':shared/models';
import { CommentOption } from ':shared/models/comment-option';
import { RestaurantAiSettings, RestaurantAiSettingsDispatch } from ':shared/models/restaurant-ai-settings';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

export interface LangOption {
    value: ApplicationLanguage;
    text: string;
}

export interface EditRestaurantAiSettingsModalInputData {
    restaurantAiSettings: RestaurantAiSettings;
    selectedKeywords: Keyword[];
}

@Component({
    selector: 'app-edit-ai-review-settings-modal',
    templateUrl: './edit-ai-review-settings-modal.component.html',
    styleUrls: ['./edit-ai-review-settings-modal.component.scss'],
    imports: [
        NgClass,
        NgTemplateOutlet,
        MatButtonModule,
        MatIconModule,
        MatTabsModule,
        MatTooltipModule,
        TranslateModule,
        AiAdvancedReviewSettingsModalTabComponent,
        CloseWithoutSavingModalComponent,
        AiGeneralReviewSettingsModalTabComponent,
        MalouSpinnerComponent,
        StarGaugeComponent,
        SelectComponent,
        FormsModule,
        ReactiveFormsModule,
        ImagePathResolverPipe,
        KeywordsScoreGaugeComponent,
        SelectLanguagesComponent,
        AsyncPipe,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditAiReviewSettingsModalComponent implements OnInit {
    @ViewChild('keywordsScoreGauge') keywordsScoreGauge: KeywordsScoreGaugeComponent;
    readonly aiSettingsContext = inject(AiSettingsContext);

    private readonly _data: EditRestaurantAiSettingsModalInputData = inject(MAT_DIALOG_DATA);
    private readonly _dialogRef = inject(MatDialogRef<EditAiReviewSettingsModalComponent>);
    private readonly _screenSizeService = inject(ScreenSizeService);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);
    private readonly _aiService = inject(AiService);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _reviewsService = inject(ReviewsService);

    readonly APP_LANGUAGES = Object.values(ApplicationLanguage);
    readonly SvgIcon = SvgIcon;
    readonly SelectLanguagesDisplayStyle = SelectLanguagesDisplayStyle;
    readonly KeywordsScoreGaugeType = KeywordsScoreGaugeType;

    readonly selectedKeywords = signal<Keyword[]>([]);

    readonly aiReviewSettingsForm = new FormGroup({
        replyTone: new FormControl<FrenchTutoiementVouvoiement>(FrenchTutoiementVouvoiement.DOES_NOT_MATTER, {
            validators: [Validators.required],
            nonNullable: true,
        }),
        catchphrase: new FormControl<string>('', {
            nonNullable: true,
        }),
        customerNaming: new FormControl<CustomerNaming>(CustomerNaming.TITLE_AND_LASTNAME, {
            validators: [Validators.required],
            nonNullable: true,
        }),
        shouldTranslateCatchphrase: new FormControl<boolean>(false, {
            validators: [Validators.required],
            nonNullable: true,
        }),
        signatures: new FormArray([
            new FormControl<string>('', {
                nonNullable: true,
            }),
        ]),
        restaurantKeywordIds: new FormControl<string[]>([], { nonNullable: true }),
        forbiddenWords: new FormControl<string[]>([], {
            nonNullable: true,
        }),
        shouldTranslateSignature: new FormControl<boolean>(false, {
            validators: [Validators.required],
            nonNullable: true,
        }),
        prompt: new FormControl<string>(''),
    });

    get signatures(): FormArray<FormControl<string>> {
        return this.aiReviewSettingsForm.controls.signatures;
    }

    readonly isFormValid$ = this.aiReviewSettingsForm.valueChanges.pipe(map(() => this.aiReviewSettingsForm.valid));

    readonly displayCloseModal = signal(false);
    readonly isSubmitting = signal(false);
    readonly currentDate = signal(formatDate(new Date()));

    readonly AVAILABLE_COMMENT_OPTIONS: CommentOption[] = this._computeAvailableCommentOptions();

    readonly previewReviewReplyForm = new FormGroup({
        withComment: new FormControl(this.AVAILABLE_COMMENT_OPTIONS[0]),
        chosenLanguageForPreview: new FormControl<ApplicationLanguage | null>(null),
    });

    get chosenLanguageForPreview(): FormControl<ApplicationLanguage> {
        return this.previewReviewReplyForm.controls.chosenLanguageForPreview as FormControl<ApplicationLanguage>;
    }

    readonly currentWithCommentOption = signal<string>(this.AVAILABLE_COMMENT_OPTIONS[0].value);

    readonly isLoadingPreview = signal<boolean>(false);
    readonly previewReviewReply = signal<string>('');

    readonly formattedPreviewReviewReply = computed(() =>
        highlightKeywordsInText({
            text: this.previewReviewReply(),
            keywords: this._data.selectedKeywords,
            restaurantName: this.aiSettingsContext.restaurantAiSettings()?.restaurantName,
            currentLang: this.chosenLanguageForPreview.value,
        })
    );

    readonly rating = signal<number>(5);
    readonly review = signal<Review | null>(null);
    readonly reviewerName = computed(() => this.review()?.reviewer?.displayName || '');
    readonly reviewText = computed(() => this.review()?.text || '');
    readonly reviewerNameValidation = computed(() => this.review()?.reviewerNameValidation || DEFAULT_REVIEWER_NAME_VALIDATION);

    readonly oneHour = signal<number>(1);
    readonly keywords = signal<Keyword[]>(this._data.selectedKeywords);
    readonly restaurant = toSignal(this._restaurantsService.restaurantSelected$, { initialValue: null });
    readonly textType = signal<KeywordScoreTextType>(KeywordScoreTextType.HIGH_RATE_REVIEW);
    readonly lang = signal<ApplicationLanguage | null>(null);
    readonly keywordsIndicationList: WritableSignal<Indication[]> = signal([]);
    readonly aiReviewSettings: WritableSignal<RestaurantAiSettings | undefined> = signal(undefined);

    readonly isPhoneScreen = toSignal(this._screenSizeService.isPhoneScreen$, { initialValue: this._screenSizeService.isPhoneScreen });

    constructor() {
        this.selectedKeywords.set(this._data.selectedKeywords);
        this.previewReviewReplyForm.patchValue({
            chosenLanguageForPreview: this.aiSettingsContext.restaurantAiSettings()!.defaultLanguageResponse,
        });
    }

    ngOnInit(): void {
        this._initializeReviewSettingsForm();

        this.aiReviewSettingsForm.valueChanges.pipe(
            filter((form) => isObjectRequired(form)),
            tap((form) => {
                this.aiReviewSettings.set(
                    new RestaurantAiSettings({
                        ...this.aiSettingsContext.restaurantAiSettings()!,
                        reviewSettings: {
                            customerNaming: form.customerNaming!,
                            forbiddenWords: form.forbiddenWords!,
                            replyTone: form.replyTone!,
                            restaurantKeywordIds: form.restaurantKeywordIds!,
                            shouldTranslateCatchphrase: form.shouldTranslateCatchphrase!,
                            shouldTranslateSignature: form.shouldTranslateSignature!,
                            catchphrase: form.catchphrase!,
                            signatures: form.signatures!,
                            signatureTranslationIds: [],
                            prompt: form.prompt ?? null,
                        },
                    })
                );
            })
        );

        this.chosenLanguageForPreview.valueChanges.subscribe((lang) => {
            this.lang.set(lang);
        });

        this._selectReviewRelatedToCommentOption(this.AVAILABLE_COMMENT_OPTIONS[0]);
    }

    close(): void {
        if (this.isSubmitting()) {
            return;
        }
        this.confirmClose();
    }

    confirmClose(): void {
        this._dialogRef.close();
    }

    displayOption(option: { text: string }): string {
        return option.text;
    }

    generateResponsePreview(): void {
        const aiReviewSettingsForm = this.aiReviewSettingsForm.value;
        const restaurantAiSettings = this.aiSettingsContext.restaurantAiSettings()!;
        // only to narrow the type
        if (isObjectRequired(aiReviewSettingsForm)) {
            this.isLoadingPreview.set(true);

            if (!aiReviewSettingsForm) {
                this._toastService.openErrorToast(
                    this._translateService.instant('restaurant_ai_settings.modals.upsert.error_generating_preview')
                );
                return;
            }
            this._aiService
                .answerReviewPreview({
                    restaurantId: restaurantAiSettings.restaurantId,
                    restaurantAiSettings: new RestaurantAiSettings({
                        ...restaurantAiSettings!,
                        reviewSettings: {
                            ...aiReviewSettingsForm,
                            // we use "!" because in the UI we say "Do not translate" --> checked at true (we should have name the field differently)
                            shouldTranslateCatchphrase: !aiReviewSettingsForm.shouldTranslateCatchphrase,
                            // we use "!" because in the UI we say "Do not translate" --> checked at true (we should have name the field differently)
                            shouldTranslateSignature: !aiReviewSettingsForm.shouldTranslateSignature,
                        },
                    }),
                    sourceLanguage: this.chosenLanguageForPreview.value,
                    lang: this.chosenLanguageForPreview.value,
                    reviewerName: this.reviewerName(),
                    text: this.reviewText(),
                })
                .subscribe({
                    next: (res) => {
                        this.isLoadingPreview.set(false);
                        this.previewReviewReply.set(res.data);
                    },
                    error: (err) => {
                        this.isLoadingPreview.set(false);
                        console.warn(err);
                        this._toastService.openErrorToast(
                            this._translateService.instant('restaurant_ai_settings.modals.upsert.error_generating_preview')
                        );
                    },
                });
        }
    }

    async submit(): Promise<void> {
        if (this.aiReviewSettingsForm.invalid) {
            return;
        }
        const aiReviewSettingsForm = this.aiReviewSettingsForm.value;
        // just to narrow the type
        if (isObjectRequired(aiReviewSettingsForm)) {
            this.isSubmitting.set(true);

            try {
                await this.aiSettingsContext.updateAiSettings({
                    restaurantAiSettings: new RestaurantAiSettings({
                        ...this.aiSettingsContext.restaurantAiSettings()!,
                        reviewSettings: {
                            ...aiReviewSettingsForm,
                            shouldTranslateCatchphrase: !aiReviewSettingsForm.shouldTranslateCatchphrase,
                            shouldTranslateSignature: !aiReviewSettingsForm.shouldTranslateSignature,
                            signatures: aiReviewSettingsForm.signatures.filter((signature) => signature.trim() !== ''),
                        },
                    }),
                    dispatcher: RestaurantAiSettingsDispatch.review,
                });
                this.confirmClose();
                this._toastService.openSuccessToast(this._translateService.instant('restaurant_ai_settings.modals.upsert.success'));
                this.isSubmitting.set(false);
            } catch (e) {
                this._toastService.openErrorToast(
                    this._translateService.instant('restaurant_ai_settings.modals.upsert.errors.upsert_error')
                );
                this.isSubmitting.set(false);
            }
        }
    }

    onWithCommentChange(event: CommentOption): void {
        this.currentWithCommentOption.set(event.value);
        this._selectReviewRelatedToCommentOption(event);
    }

    private _selectReviewRelatedToCommentOption(option: CommentOption): void {
        this.rating.set(Number(option.value));
        this._reviewsService
            .getReviewRelatedToCommentOption(this.aiSettingsContext.restaurantAiSettings()!.restaurantId, option)
            .subscribe({
                next: (review) => {
                    if (!review) {
                        return;
                    }
                    this.review.set(review);
                },
            });
    }

    private _computeAvailableCommentOptions(): CommentOption[] {
        const options: { value: string; text: string }[] = [];
        for (let i = 5; i >= 1; i--) {
            options.push(this._computeCommentOption(i, CommentOptionValue.WITH));
        }
        return options;
    }

    private _computeCommentOption(rating: number, withComment: CommentOptionValue): CommentOption {
        return {
            value: rating.toString(),
            text:
                withComment === CommentOptionValue.WITH
                    ? this._translateService.instant('restaurant_ai_settings.modals.upsert.with_comments', { stars: rating })
                    : this._translateService.instant('restaurant_ai_settings.modals.upsert.without_comments', { stars: rating }),
        };
    }

    private _initializeReviewSettingsForm(): void {
        const settings = this.aiSettingsContext.restaurantAiSettings();
        if (settings) {
            this.aiReviewSettingsForm.patchValue({
                catchphrase: settings.reviewSettings.catchphrase,
                customerNaming: settings.reviewSettings.customerNaming,
                replyTone: settings.reviewSettings.replyTone,
                // we use "!" because in the UI we say "Do not translate" --> checked at true (we should have name the field differently)
                shouldTranslateCatchphrase: !settings.reviewSettings.shouldTranslateCatchphrase,
                // we use "!" because in the UI we say "Do not translate" --> checked at true (we should have name the field differently)
                shouldTranslateSignature: !settings.reviewSettings.shouldTranslateSignature,
                forbiddenWords: settings.reviewSettings.forbiddenWords,
                restaurantKeywordIds: settings.reviewSettings.restaurantKeywordIds,
                prompt: settings.reviewSettings.prompt ?? null,
            });
            if (settings.reviewSettings.signatures.length > 0) {
                this.signatures.clear();
                settings.reviewSettings.signatures.forEach((signature) =>
                    this.signatures.push(
                        new FormControl<string>(signature, {
                            nonNullable: true,
                        })
                    )
                );
            }
        }
    }
}
