import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { PlatformAccessStatus, PlatformAccessType, PlatformKey } from '@malou-io/package-utils';

import { RestaurantsService } from ':core/services/restaurants.service';
import { AuthorizedAccountManagedPlatformKeys } from ':modules/platforms/platforms-connection-modals/platforms-connection-account-managed-modal/account-managed-connection-modal.service';
import { ApiResult, Restaurant } from ':shared/models';

export type PlatformKeysWithAutoAccessValidation = PlatformKey.SEVENROOMS;
export const platformKeysWithAutoAccessValidation: PlatformKey[] = [PlatformKey.SEVENROOMS];

@Injectable({
    providedIn: 'root',
})
export class PlatformAccessService {
    private readonly _restaurantsService = inject(RestaurantsService);

    updatePlatformAccess(
        platformKey: AuthorizedAccountManagedPlatformKeys,
        options: { isValid: boolean }
    ): Observable<ApiResult<Restaurant>> {
        const restaurantId = this._restaurantsService.currentRestaurant._id;
        return this._restaurantsService.createPlatformAccess(restaurantId, {
            platformKey,
            accessType: PlatformAccessType.MANAGER,
            status:
                platformKeysWithAutoAccessValidation.includes(platformKey) && options.isValid
                    ? PlatformAccessStatus.VERIFIED
                    : PlatformAccessStatus.NEED_REVIEW,
            lastUpdated: new Date(),
            lastVerified: undefined,
            active: true,
        });
    }

    validatePlatformAccess(platformKey: PlatformKeysWithAutoAccessValidation, socialId: string): Observable<boolean> {
        const restaurantId = this._restaurantsService.currentRestaurant._id;
        return this._restaurantsService.validatePlatformAccess(restaurantId, { platformKey, socialId });
    }
}
