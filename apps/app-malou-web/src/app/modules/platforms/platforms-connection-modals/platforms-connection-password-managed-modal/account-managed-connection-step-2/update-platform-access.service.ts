import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { PlatformAccessStatus, PlatformAccessType } from '@malou-io/package-utils';

import { RestaurantsService } from ':core/services/restaurants.service';
import { AuthorizedPasswordManagedPlatformKeys } from ':modules/platforms/platforms-connection-modals/platforms-connection-password-managed-modal/password-managed-connection-modal.service';
import { ApiResult, Restaurant } from ':shared/models';

@Injectable({
    providedIn: 'root',
})
export class UpdatePlatformAccessService {
    private readonly _restaurantsService = inject(RestaurantsService);

    execute(
        platformKey: AuthorizedPasswordManagedPlatformKeys,
        data: { login: string; password: string }
    ): Observable<ApiResult<Restaurant>> {
        const restaurantId = this._restaurantsService.currentRestaurant._id;
        return this._restaurantsService.createPlatformAccess(restaurantId, {
            platformKey,
            accessType: PlatformAccessType.CREDENTIALS,
            data,
            status: PlatformAccessStatus.NEED_REVIEW,
            lastUpdated: new Date(),
            lastVerified: undefined,
            active: true,
        });
    }
}
