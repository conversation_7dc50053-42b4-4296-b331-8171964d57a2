import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { ShiftDto } from '@malou-io/package-dto';
import { ApiResultV2, PlatformKey } from '@malou-io/package-utils';

import { environment } from ':environments/environment';

@Injectable({
    providedIn: 'root',
})
export class ShiftsService {
    readonly API_BASE_URL = `${environment.APP_MALOU_API_URL}/api/v1/shifts`;

    constructor(private readonly _http: HttpClient) {}

    getRestaurantsShifts(platformKey: PlatformKey, restaurantId: string): Observable<ApiResultV2<ShiftDto[]>> {
        return this._http.get<ApiResultV2<ShiftDto[]>>(`${this.API_BASE_URL}/${platformKey}/restaurants/${restaurantId}`);
    }
}
