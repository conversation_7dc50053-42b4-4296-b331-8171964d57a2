import { DiagnosticDto } from '@malou-io/package-dto';

import { Illustration } from ':shared/pipes';

export interface LoaderStep {
    type: StepType;
    stepAction: (diagnosticId: string) => Promise<DiagnosticDto | undefined>;
    illustration: Illustration;
}

export enum LoaderStepStatus {
    DONE = 'done',
    CURRENT = 'current',
    TO_BE_DONE = 'to be done',
}

export enum StepType {
    GOOGLE = 'google',
    OTHER_PLATFORMS = 'other_platforms',
    COMPETITORS = 'competitors',
    INSTAGRAM = 'instagram',
}
