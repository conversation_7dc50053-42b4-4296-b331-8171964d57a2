<div class="flex h-full flex-col items-center gap-10 md:px-6 md:py-8">
    @if (!hasStartedDiagnosticComputation()) {
        <app-malou-spinner class="mt-24" [size]="'medium'" [color]="'#6A52FD'"></app-malou-spinner>
    } @else {
        <app-diagnostic-header [subtitle]="'maloupe.diagnostic_loader.subtitle' | translate"></app-diagnostic-header>

        <div
            class="flex h-44 flex-col items-center"
            [ngClass]="{
                'ml-10': currentIllustration() === Illustration.OK_HAND,
            }">
            <img class="max-h-44" [src]="currentIllustration() | illustrationPathResolver" />
        </div>
        <ng-container [ngTemplateOutlet]="loaderTemplate"></ng-container>
    }
</div>

<ng-template #loaderTemplate>
    <div class="mb-20 flex items-center">
        @for (step of currentSteps(); let index = $index; track $index) {
            @let stepStatus = getStepStatus | applyPure: index : currentStepIndex();

            @if (index === 0) {
                <div
                    class="step-link hidden"
                    [ngClass]="{
                        'md:block': index === currentStepIndex(),
                    }"></div>
            }

            <div
                class="step relative flex items-center justify-center rounded-full px-3 py-1"
                [ngClass]="{
                    'bg-malou-state-success': index < currentStepIndex(),
                    'bg-malou-state-success-light': index === currentStepIndex(),
                    'bg-malou-text/20': index > currentStepIndex(),
                    'md:hidden': index !== currentStepIndex(),
                }">
                @switch (stepStatus) {
                    @case (LoaderStepStatus.DONE) {
                        <mat-icon class="!h-4 !w-4 text-malou-white" [svgIcon]="SvgIcon.CHECK"></mat-icon>
                    }
                    @case (LoaderStepStatus.CURRENT) {
                        <app-malou-spinner [size]="'small'" [color]="'#34B467'"></app-malou-spinner>
                    }
                    @case (LoaderStepStatus.TO_BE_DONE) {
                        <mat-icon class="!h-4 !w-4 text-malou-white" [svgIcon]="SvgIcon.CHECK"></mat-icon>
                    }
                }

                <div
                    class="absolute text-center text-xs font-semibold text-malou-text"
                    [ngClass]="{
                        'step-label top-9': index !== currentStepIndex(),
                        'current-step-label top-10 !text-malou-state-success': index === currentStepIndex(),
                    }">
                    {{ step.type | enumTranslate: 'loader_step_title' }}
                </div>
            </div>

            @if (index !== currentSteps().length - 1) {
                <div
                    class="step-link h-[1px] border"
                    [ngClass]="{
                        'border-dashed': index - currentStepIndex() >= -1,
                        'border-malou-state-success': index - currentStepIndex() < -1,
                        'gradient-border-success': index - currentStepIndex() === -1,
                        'border-malou-text/20': index - currentStepIndex() >= 0,
                        'md:hidden': index !== currentStepIndex() && index !== currentStepIndex() - 1,
                    }"></div>
            } @else {
                <div
                    class="step-link hidden"
                    [ngClass]="{
                        'md:block': index === currentStepIndex(),
                    }"></div>
            }
        }
    </div>
</ng-template>
